{"ast": null, "code": "export { default } from \"./getDisplayName.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/Desktop/meror/frontend/node_modules/@mui/utils/esm/getDisplayName/index.js"], "sourcesContent": ["export { default } from \"./getDisplayName.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}