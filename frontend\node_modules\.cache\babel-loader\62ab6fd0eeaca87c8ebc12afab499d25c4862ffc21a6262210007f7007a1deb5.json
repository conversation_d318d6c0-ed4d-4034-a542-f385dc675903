{"ast": null, "code": "import formatLocale from \"./locale.js\";\nvar locale;\nexport var timeFormat;\nexport var timeParse;\nexport var utcFormat;\nexport var utcParse;\ndefaultLocale({\n  dateTime: \"%x, %X\",\n  date: \"%-m/%-d/%Y\",\n  time: \"%-I:%M:%S %p\",\n  periods: [\"AM\", \"PM\"],\n  days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n  shortDays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n  shortMonths: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"]\n});\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  timeFormat = locale.format;\n  timeParse = locale.parse;\n  utcFormat = locale.utcFormat;\n  utcParse = locale.utcParse;\n  return locale;\n}", "map": {"version": 3, "names": ["formatLocale", "locale", "timeFormat", "timeParse", "utcFormat", "utcParse", "defaultLocale", "dateTime", "date", "time", "periods", "days", "shortDays", "months", "shortMonths", "definition", "format", "parse"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-time-format/src/defaultLocale.js"], "sourcesContent": ["import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var timeFormat;\nexport var timeParse;\nexport var utcFormat;\nexport var utcParse;\n\ndefaultLocale({\n  dateTime: \"%x, %X\",\n  date: \"%-m/%-d/%Y\",\n  time: \"%-I:%M:%S %p\",\n  periods: [\"AM\", \"PM\"],\n  days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n  shortDays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n  shortMonths: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  timeFormat = locale.format;\n  timeParse = locale.parse;\n  utcFormat = locale.utcFormat;\n  utcParse = locale.utcParse;\n  return locale;\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,aAAa;AAEtC,IAAIC,MAAM;AACV,OAAO,IAAIC,UAAU;AACrB,OAAO,IAAIC,SAAS;AACpB,OAAO,IAAIC,SAAS;AACpB,OAAO,IAAIC,QAAQ;AAEnBC,aAAa,CAAC;EACZC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACrBC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;EACpFC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC5DC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;EAClIC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AAClG,CAAC,CAAC;AAEF,eAAe,SAASR,aAAaA,CAACS,UAAU,EAAE;EAChDd,MAAM,GAAGD,YAAY,CAACe,UAAU,CAAC;EACjCb,UAAU,GAAGD,MAAM,CAACe,MAAM;EAC1Bb,SAAS,GAAGF,MAAM,CAACgB,KAAK;EACxBb,SAAS,GAAGH,MAAM,CAACG,SAAS;EAC5BC,QAAQ,GAAGJ,MAAM,CAACI,QAAQ;EAC1B,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}