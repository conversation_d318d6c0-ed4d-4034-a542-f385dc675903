{"ast": null, "code": "import { InternMap } from \"internmap\";\nexport default function mode(values, valueof) {\n  const counts = new InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}", "map": {"version": 3, "names": ["InternMap", "mode", "values", "valueof", "counts", "undefined", "value", "set", "get", "index", "modeValue", "modeCount", "count"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-array/src/mode.js"], "sourcesContent": ["import {InternMap} from \"internmap\";\n\nexport default function mode(values, valueof) {\n  const counts = new InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,IAAIA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC5C,MAAMC,MAAM,GAAG,IAAIJ,SAAS,CAAC,CAAC;EAC9B,IAAIG,OAAO,KAAKE,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIJ,MAAM,EAAE;MACxB,IAAII,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAIA,KAAK,EAAE;QACnCF,MAAM,CAACG,GAAG,CAACD,KAAK,EAAE,CAACF,MAAM,CAACI,GAAG,CAACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACjD;IACF;EACF,CAAC,MAAM;IACL,IAAIG,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIH,KAAK,IAAIJ,MAAM,EAAE;MACxB,IAAI,CAACI,KAAK,GAAGH,OAAO,CAACG,KAAK,EAAE,EAAEG,KAAK,EAAEP,MAAM,CAAC,KAAK,IAAI,IAAII,KAAK,IAAIA,KAAK,EAAE;QACvEF,MAAM,CAACG,GAAG,CAACD,KAAK,EAAE,CAACF,MAAM,CAACI,GAAG,CAACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACjD;IACF;EACF;EACA,IAAII,SAAS;EACb,IAAIC,SAAS,GAAG,CAAC;EACjB,KAAK,MAAM,CAACL,KAAK,EAAEM,KAAK,CAAC,IAAIR,MAAM,EAAE;IACnC,IAAIQ,KAAK,GAAGD,SAAS,EAAE;MACrBA,SAAS,GAAGC,KAAK;MACjBF,SAAS,GAAGJ,KAAK;IACnB;EACF;EACA,OAAOI,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}