{"ast": null, "code": "import value from \"./value.js\";\nexport default function (a, b) {\n  var i = {},\n    c = {},\n    k;\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n  return function (t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}", "map": {"version": 3, "names": ["value", "a", "b", "i", "c", "k", "t"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-interpolate/src/object.js"], "sourcesContent": ["import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAE9B,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC;EAEL,IAAIJ,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC/C,IAAIC,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAEA,CAAC,GAAG,CAAC,CAAC;EAE/C,KAAKG,CAAC,IAAIH,CAAC,EAAE;IACX,IAAIG,CAAC,IAAIJ,CAAC,EAAE;MACVE,CAAC,CAACE,CAAC,CAAC,GAAGL,KAAK,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;IACb;EACF;EAEA,OAAO,UAASC,CAAC,EAAE;IACjB,KAAKD,CAAC,IAAIF,CAAC,EAAEC,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAACC,CAAC,CAAC;IAC3B,OAAOF,CAAC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}