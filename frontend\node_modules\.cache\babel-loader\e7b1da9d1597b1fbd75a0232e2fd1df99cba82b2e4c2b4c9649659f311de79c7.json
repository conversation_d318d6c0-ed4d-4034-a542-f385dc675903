{"ast": null, "code": "import decompose, { identity } from \"./decompose.js\";\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}", "map": {"version": 3, "names": ["decompose", "identity", "svgNode", "parseCss", "value", "m", "DOMMatrix", "WebKitCSSMatrix", "isIdentity", "a", "b", "c", "d", "e", "f", "parseSvg", "document", "createElementNS", "setAttribute", "transform", "baseVal", "consolidate", "matrix"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-interpolate/src/transform/parse.js"], "sourcesContent": ["import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,IAAGC,QAAQ,QAAO,gBAAgB;AAElD,IAAIC,OAAO;;AAEX;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,MAAMC,CAAC,GAAG,KAAK,OAAOC,SAAS,KAAK,UAAU,GAAGA,SAAS,GAAGC,eAAe,EAAEH,KAAK,GAAG,EAAE,CAAC;EACzF,OAAOC,CAAC,CAACG,UAAU,GAAGP,QAAQ,GAAGD,SAAS,CAACK,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,EAAEL,CAAC,CAACM,CAAC,EAAEN,CAAC,CAACO,CAAC,EAAEP,CAAC,CAACQ,CAAC,EAAER,CAAC,CAACS,CAAC,CAAC;AAC1E;AAEA,OAAO,SAASC,QAAQA,CAACX,KAAK,EAAE;EAC9B,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAOH,QAAQ;EAClC,IAAI,CAACC,OAAO,EAAEA,OAAO,GAAGc,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,GAAG,CAAC;EACnFf,OAAO,CAACgB,YAAY,CAAC,WAAW,EAAEd,KAAK,CAAC;EACxC,IAAI,EAAEA,KAAK,GAAGF,OAAO,CAACiB,SAAS,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,EAAE,OAAOpB,QAAQ;EACvEG,KAAK,GAAGA,KAAK,CAACkB,MAAM;EACpB,OAAOtB,SAAS,CAACI,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,EAAEN,KAAK,CAACO,CAAC,EAAEP,KAAK,CAACQ,CAAC,EAAER,KAAK,CAACS,CAAC,EAAET,KAAK,CAACU,CAAC,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}