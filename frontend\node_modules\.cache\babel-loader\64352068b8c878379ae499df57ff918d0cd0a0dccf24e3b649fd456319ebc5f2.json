{"ast": null, "code": "import { IMPORT, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES } from './Enum.js';\nimport { strlen, sizeof } from './Utility.js';\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize(children, callback) {\n  var output = '';\n  var length = sizeof(children);\n  for (var i = 0; i < length; i++) output += callback(children[i], i, children, callback) || '';\n  return output;\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify(element, index, children, callback) {\n  switch (element.type) {\n    case LAYER:\n      if (element.children.length) break;\n    case IMPORT:\n    case DECLARATION:\n      return element.return = element.return || element.value;\n    case COMMENT:\n      return '';\n    case KEYFRAMES:\n      return element.return = element.value + '{' + serialize(element.children, callback) + '}';\n    case RULESET:\n      element.value = element.props.join(',');\n  }\n  return strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : '';\n}", "map": {"version": 3, "names": ["IMPORT", "LAYER", "COMMENT", "RULESET", "DECLARATION", "KEYFRAMES", "strlen", "sizeof", "serialize", "children", "callback", "output", "length", "i", "stringify", "element", "index", "type", "return", "value", "props", "join"], "sources": ["D:/Desktop/meror/frontend/node_modules/stylis/src/Serializer.js"], "sourcesContent": ["import {IMPOR<PERSON>, LAYER, COMMENT, RULESE<PERSON>, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen, sizeof} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\tvar length = sizeof(children)\n\n\tfor (var i = 0; i < length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: element.value = element.props.join(',')\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n"], "mappings": "AAAA,SAAQA,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAO,WAAW;AACjF,SAAQC,MAAM,EAAEC,MAAM,QAAO,cAAc;;AAE3C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,MAAM,GAAGL,MAAM,CAACE,QAAQ,CAAC;EAE7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAC9BF,MAAM,IAAID,QAAQ,CAACD,QAAQ,CAACI,CAAC,CAAC,EAAEA,CAAC,EAAEJ,QAAQ,EAAEC,QAAQ,CAAC,IAAI,EAAE;EAE7D,OAAOC,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,SAASA,CAAEC,OAAO,EAAEC,KAAK,EAAEP,QAAQ,EAAEC,QAAQ,EAAE;EAC9D,QAAQK,OAAO,CAACE,IAAI;IACnB,KAAKhB,KAAK;MAAE,IAAIc,OAAO,CAACN,QAAQ,CAACG,MAAM,EAAE;IACzC,KAAKZ,MAAM;IAAE,KAAKI,WAAW;MAAE,OAAOW,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACI,KAAK;IACtF,KAAKjB,OAAO;MAAE,OAAO,EAAE;IACvB,KAAKG,SAAS;MAAE,OAAOU,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACI,KAAK,GAAG,GAAG,GAAGX,SAAS,CAACO,OAAO,CAACN,QAAQ,EAAEC,QAAQ,CAAC,GAAG,GAAG;IACzG,KAAKP,OAAO;MAAEY,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACK,KAAK,CAACC,IAAI,CAAC,GAAG,CAAC;EACtD;EAEA,OAAOf,MAAM,CAACG,QAAQ,GAAGD,SAAS,CAACO,OAAO,CAACN,QAAQ,EAAEC,QAAQ,CAAC,CAAC,GAAGK,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACI,KAAK,GAAG,GAAG,GAAGV,QAAQ,GAAG,GAAG,GAAG,EAAE;AAC7H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}