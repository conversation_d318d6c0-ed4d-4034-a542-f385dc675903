{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isMatch = require('./isMatch.js');\nconst isObject = require('./isObject.js');\nconst isPrimitive = require('../../predicate/isPrimitive.js');\nconst eq = require('../util/eq.js');\nfunction isMatchWith(target, source, compare) {\n  if (typeof compare !== 'function') {\n    return isMatch.isMatch(target, source);\n  }\n  return isMatchWithInternal(target, source, function doesMatch(objValue, srcValue, key, object, source, stack) {\n    const isEqual = compare(objValue, srcValue, key, object, source, stack);\n    if (isEqual !== undefined) {\n      return Boolean(isEqual);\n    }\n    return isMatchWithInternal(objValue, srcValue, doesMatch, stack);\n  }, new Map());\n}\nfunction isMatchWithInternal(target, source, compare, stack) {\n  if (source === target) {\n    return true;\n  }\n  switch (typeof source) {\n    case 'object':\n      {\n        return isObjectMatch(target, source, compare, stack);\n      }\n    case 'function':\n      {\n        const sourceKeys = Object.keys(source);\n        if (sourceKeys.length > 0) {\n          return isMatchWithInternal(target, {\n            ...source\n          }, compare, stack);\n        }\n        return eq.eq(target, source);\n      }\n    default:\n      {\n        if (!isObject.isObject(target)) {\n          return eq.eq(target, source);\n        }\n        if (typeof source === 'string') {\n          return source === '';\n        }\n        return true;\n      }\n  }\n}\nfunction isObjectMatch(target, source, compare, stack) {\n  if (source == null) {\n    return true;\n  }\n  if (Array.isArray(source)) {\n    return isArrayMatch(target, source, compare, stack);\n  }\n  if (source instanceof Map) {\n    return isMapMatch(target, source, compare, stack);\n  }\n  if (source instanceof Set) {\n    return isSetMatch(target, source, compare, stack);\n  }\n  const keys = Object.keys(source);\n  if (target == null) {\n    return keys.length === 0;\n  }\n  if (keys.length === 0) {\n    return true;\n  }\n  if (stack && stack.has(source)) {\n    return stack.get(source) === target;\n  }\n  if (stack) {\n    stack.set(source, target);\n  }\n  try {\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      if (!isPrimitive.isPrimitive(target) && !(key in target)) {\n        return false;\n      }\n      if (source[key] === undefined && target[key] !== undefined) {\n        return false;\n      }\n      if (source[key] === null && target[key] !== null) {\n        return false;\n      }\n      const isEqual = compare(target[key], source[key], key, target, source, stack);\n      if (!isEqual) {\n        return false;\n      }\n    }\n    return true;\n  } finally {\n    if (stack) {\n      stack.delete(source);\n    }\n  }\n}\nfunction isMapMatch(target, source, compare, stack) {\n  if (source.size === 0) {\n    return true;\n  }\n  if (!(target instanceof Map)) {\n    return false;\n  }\n  for (const [key, sourceValue] of source.entries()) {\n    const targetValue = target.get(key);\n    const isEqual = compare(targetValue, sourceValue, key, target, source, stack);\n    if (isEqual === false) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isArrayMatch(target, source, compare, stack) {\n  if (source.length === 0) {\n    return true;\n  }\n  if (!Array.isArray(target)) {\n    return false;\n  }\n  const countedIndex = new Set();\n  for (let i = 0; i < source.length; i++) {\n    const sourceItem = source[i];\n    let found = false;\n    for (let j = 0; j < target.length; j++) {\n      if (countedIndex.has(j)) {\n        continue;\n      }\n      const targetItem = target[j];\n      let matches = false;\n      const isEqual = compare(targetItem, sourceItem, i, target, source, stack);\n      if (isEqual) {\n        matches = true;\n      }\n      if (matches) {\n        countedIndex.add(j);\n        found = true;\n        break;\n      }\n    }\n    if (!found) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isSetMatch(target, source, compare, stack) {\n  if (source.size === 0) {\n    return true;\n  }\n  if (!(target instanceof Set)) {\n    return false;\n  }\n  return isArrayMatch([...target], [...source], compare, stack);\n}\nexports.isMatchWith = isMatchWith;\nexports.isSetMatch = isSetMatch;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isMatch", "require", "isObject", "isPrimitive", "eq", "isMatchWith", "target", "source", "compare", "isMatchWithInternal", "doesMatch", "objValue", "srcValue", "key", "object", "stack", "isEqual", "undefined", "Boolean", "Map", "isObjectMatch", "sourceKeys", "keys", "length", "Array", "isArray", "isArrayMatch", "isMapMatch", "Set", "isSetMatch", "has", "get", "set", "i", "delete", "size", "sourceValue", "entries", "targetValue", "countedIndex", "sourceItem", "found", "j", "targetItem", "matches", "add"], "sources": ["D:/Desktop/meror/frontend/node_modules/es-toolkit/dist/compat/predicate/isMatchWith.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isMatch = require('./isMatch.js');\nconst isObject = require('./isObject.js');\nconst isPrimitive = require('../../predicate/isPrimitive.js');\nconst eq = require('../util/eq.js');\n\nfunction isMatchWith(target, source, compare) {\n    if (typeof compare !== 'function') {\n        return isMatch.isMatch(target, source);\n    }\n    return isMatchWithInternal(target, source, function doesMatch(objValue, srcValue, key, object, source, stack) {\n        const isEqual = compare(objValue, srcValue, key, object, source, stack);\n        if (isEqual !== undefined) {\n            return Boolean(isEqual);\n        }\n        return isMatchWithInternal(objValue, srcValue, doesMatch, stack);\n    }, new Map());\n}\nfunction isMatchWithInternal(target, source, compare, stack) {\n    if (source === target) {\n        return true;\n    }\n    switch (typeof source) {\n        case 'object': {\n            return isObjectMatch(target, source, compare, stack);\n        }\n        case 'function': {\n            const sourceKeys = Object.keys(source);\n            if (sourceKeys.length > 0) {\n                return isMatchWithInternal(target, { ...source }, compare, stack);\n            }\n            return eq.eq(target, source);\n        }\n        default: {\n            if (!isObject.isObject(target)) {\n                return eq.eq(target, source);\n            }\n            if (typeof source === 'string') {\n                return source === '';\n            }\n            return true;\n        }\n    }\n}\nfunction isObjectMatch(target, source, compare, stack) {\n    if (source == null) {\n        return true;\n    }\n    if (Array.isArray(source)) {\n        return isArrayMatch(target, source, compare, stack);\n    }\n    if (source instanceof Map) {\n        return isMapMatch(target, source, compare, stack);\n    }\n    if (source instanceof Set) {\n        return isSetMatch(target, source, compare, stack);\n    }\n    const keys = Object.keys(source);\n    if (target == null) {\n        return keys.length === 0;\n    }\n    if (keys.length === 0) {\n        return true;\n    }\n    if (stack && stack.has(source)) {\n        return stack.get(source) === target;\n    }\n    if (stack) {\n        stack.set(source, target);\n    }\n    try {\n        for (let i = 0; i < keys.length; i++) {\n            const key = keys[i];\n            if (!isPrimitive.isPrimitive(target) && !(key in target)) {\n                return false;\n            }\n            if (source[key] === undefined && target[key] !== undefined) {\n                return false;\n            }\n            if (source[key] === null && target[key] !== null) {\n                return false;\n            }\n            const isEqual = compare(target[key], source[key], key, target, source, stack);\n            if (!isEqual) {\n                return false;\n            }\n        }\n        return true;\n    }\n    finally {\n        if (stack) {\n            stack.delete(source);\n        }\n    }\n}\nfunction isMapMatch(target, source, compare, stack) {\n    if (source.size === 0) {\n        return true;\n    }\n    if (!(target instanceof Map)) {\n        return false;\n    }\n    for (const [key, sourceValue] of source.entries()) {\n        const targetValue = target.get(key);\n        const isEqual = compare(targetValue, sourceValue, key, target, source, stack);\n        if (isEqual === false) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isArrayMatch(target, source, compare, stack) {\n    if (source.length === 0) {\n        return true;\n    }\n    if (!Array.isArray(target)) {\n        return false;\n    }\n    const countedIndex = new Set();\n    for (let i = 0; i < source.length; i++) {\n        const sourceItem = source[i];\n        let found = false;\n        for (let j = 0; j < target.length; j++) {\n            if (countedIndex.has(j)) {\n                continue;\n            }\n            const targetItem = target[j];\n            let matches = false;\n            const isEqual = compare(targetItem, sourceItem, i, target, source, stack);\n            if (isEqual) {\n                matches = true;\n            }\n            if (matches) {\n                countedIndex.add(j);\n                found = true;\n                break;\n            }\n        }\n        if (!found) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isSetMatch(target, source, compare, stack) {\n    if (source.size === 0) {\n        return true;\n    }\n    if (!(target instanceof Set)) {\n        return false;\n    }\n    return isArrayMatch([...target], [...source], compare, stack);\n}\n\nexports.isMatchWith = isMatchWith;\nexports.isSetMatch = isSetMatch;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,eAAe,CAAC;AACzC,MAAME,WAAW,GAAGF,OAAO,CAAC,gCAAgC,CAAC;AAC7D,MAAMG,EAAE,GAAGH,OAAO,CAAC,eAAe,CAAC;AAEnC,SAASI,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC1C,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;IAC/B,OAAOR,OAAO,CAACA,OAAO,CAACM,MAAM,EAAEC,MAAM,CAAC;EAC1C;EACA,OAAOE,mBAAmB,CAACH,MAAM,EAAEC,MAAM,EAAE,SAASG,SAASA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEP,MAAM,EAAEQ,KAAK,EAAE;IAC1G,MAAMC,OAAO,GAAGR,OAAO,CAACG,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEP,MAAM,EAAEQ,KAAK,CAAC;IACvE,IAAIC,OAAO,KAAKC,SAAS,EAAE;MACvB,OAAOC,OAAO,CAACF,OAAO,CAAC;IAC3B;IACA,OAAOP,mBAAmB,CAACE,QAAQ,EAAEC,QAAQ,EAAEF,SAAS,EAAEK,KAAK,CAAC;EACpE,CAAC,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;AACjB;AACA,SAASV,mBAAmBA,CAACH,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,EAAE;EACzD,IAAIR,MAAM,KAAKD,MAAM,EAAE;IACnB,OAAO,IAAI;EACf;EACA,QAAQ,OAAOC,MAAM;IACjB,KAAK,QAAQ;MAAE;QACX,OAAOa,aAAa,CAACd,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,CAAC;MACxD;IACA,KAAK,UAAU;MAAE;QACb,MAAMM,UAAU,GAAG3B,MAAM,CAAC4B,IAAI,CAACf,MAAM,CAAC;QACtC,IAAIc,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;UACvB,OAAOd,mBAAmB,CAACH,MAAM,EAAE;YAAE,GAAGC;UAAO,CAAC,EAAEC,OAAO,EAAEO,KAAK,CAAC;QACrE;QACA,OAAOX,EAAE,CAACA,EAAE,CAACE,MAAM,EAAEC,MAAM,CAAC;MAChC;IACA;MAAS;QACL,IAAI,CAACL,QAAQ,CAACA,QAAQ,CAACI,MAAM,CAAC,EAAE;UAC5B,OAAOF,EAAE,CAACA,EAAE,CAACE,MAAM,EAAEC,MAAM,CAAC;QAChC;QACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UAC5B,OAAOA,MAAM,KAAK,EAAE;QACxB;QACA,OAAO,IAAI;MACf;EACJ;AACJ;AACA,SAASa,aAAaA,CAACd,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,EAAE;EACnD,IAAIR,MAAM,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;EACf;EACA,IAAIiB,KAAK,CAACC,OAAO,CAAClB,MAAM,CAAC,EAAE;IACvB,OAAOmB,YAAY,CAACpB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,CAAC;EACvD;EACA,IAAIR,MAAM,YAAYY,GAAG,EAAE;IACvB,OAAOQ,UAAU,CAACrB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,CAAC;EACrD;EACA,IAAIR,MAAM,YAAYqB,GAAG,EAAE;IACvB,OAAOC,UAAU,CAACvB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,CAAC;EACrD;EACA,MAAMO,IAAI,GAAG5B,MAAM,CAAC4B,IAAI,CAACf,MAAM,CAAC;EAChC,IAAID,MAAM,IAAI,IAAI,EAAE;IAChB,OAAOgB,IAAI,CAACC,MAAM,KAAK,CAAC;EAC5B;EACA,IAAID,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAIR,KAAK,IAAIA,KAAK,CAACe,GAAG,CAACvB,MAAM,CAAC,EAAE;IAC5B,OAAOQ,KAAK,CAACgB,GAAG,CAACxB,MAAM,CAAC,KAAKD,MAAM;EACvC;EACA,IAAIS,KAAK,EAAE;IACPA,KAAK,CAACiB,GAAG,CAACzB,MAAM,EAAED,MAAM,CAAC;EAC7B;EACA,IAAI;IACA,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACC,MAAM,EAAEU,CAAC,EAAE,EAAE;MAClC,MAAMpB,GAAG,GAAGS,IAAI,CAACW,CAAC,CAAC;MACnB,IAAI,CAAC9B,WAAW,CAACA,WAAW,CAACG,MAAM,CAAC,IAAI,EAAEO,GAAG,IAAIP,MAAM,CAAC,EAAE;QACtD,OAAO,KAAK;MAChB;MACA,IAAIC,MAAM,CAACM,GAAG,CAAC,KAAKI,SAAS,IAAIX,MAAM,CAACO,GAAG,CAAC,KAAKI,SAAS,EAAE;QACxD,OAAO,KAAK;MAChB;MACA,IAAIV,MAAM,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIP,MAAM,CAACO,GAAG,CAAC,KAAK,IAAI,EAAE;QAC9C,OAAO,KAAK;MAChB;MACA,MAAMG,OAAO,GAAGR,OAAO,CAACF,MAAM,CAACO,GAAG,CAAC,EAAEN,MAAM,CAACM,GAAG,CAAC,EAAEA,GAAG,EAAEP,MAAM,EAAEC,MAAM,EAAEQ,KAAK,CAAC;MAC7E,IAAI,CAACC,OAAO,EAAE;QACV,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC,SACO;IACJ,IAAID,KAAK,EAAE;MACPA,KAAK,CAACmB,MAAM,CAAC3B,MAAM,CAAC;IACxB;EACJ;AACJ;AACA,SAASoB,UAAUA,CAACrB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,EAAE;EAChD,IAAIR,MAAM,CAAC4B,IAAI,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAI,EAAE7B,MAAM,YAAYa,GAAG,CAAC,EAAE;IAC1B,OAAO,KAAK;EAChB;EACA,KAAK,MAAM,CAACN,GAAG,EAAEuB,WAAW,CAAC,IAAI7B,MAAM,CAAC8B,OAAO,CAAC,CAAC,EAAE;IAC/C,MAAMC,WAAW,GAAGhC,MAAM,CAACyB,GAAG,CAAClB,GAAG,CAAC;IACnC,MAAMG,OAAO,GAAGR,OAAO,CAAC8B,WAAW,EAAEF,WAAW,EAAEvB,GAAG,EAAEP,MAAM,EAAEC,MAAM,EAAEQ,KAAK,CAAC;IAC7E,IAAIC,OAAO,KAAK,KAAK,EAAE;MACnB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASU,YAAYA,CAACpB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,EAAE;EAClD,IAAIR,MAAM,CAACgB,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO,IAAI;EACf;EACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACnB,MAAM,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,MAAMiC,YAAY,GAAG,IAAIX,GAAG,CAAC,CAAC;EAC9B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,CAACgB,MAAM,EAAEU,CAAC,EAAE,EAAE;IACpC,MAAMO,UAAU,GAAGjC,MAAM,CAAC0B,CAAC,CAAC;IAC5B,IAAIQ,KAAK,GAAG,KAAK;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,CAACiB,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACpC,IAAIH,YAAY,CAACT,GAAG,CAACY,CAAC,CAAC,EAAE;QACrB;MACJ;MACA,MAAMC,UAAU,GAAGrC,MAAM,CAACoC,CAAC,CAAC;MAC5B,IAAIE,OAAO,GAAG,KAAK;MACnB,MAAM5B,OAAO,GAAGR,OAAO,CAACmC,UAAU,EAAEH,UAAU,EAAEP,CAAC,EAAE3B,MAAM,EAAEC,MAAM,EAAEQ,KAAK,CAAC;MACzE,IAAIC,OAAO,EAAE;QACT4B,OAAO,GAAG,IAAI;MAClB;MACA,IAAIA,OAAO,EAAE;QACTL,YAAY,CAACM,GAAG,CAACH,CAAC,CAAC;QACnBD,KAAK,GAAG,IAAI;QACZ;MACJ;IACJ;IACA,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASZ,UAAUA,CAACvB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEO,KAAK,EAAE;EAChD,IAAIR,MAAM,CAAC4B,IAAI,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAI,EAAE7B,MAAM,YAAYsB,GAAG,CAAC,EAAE;IAC1B,OAAO,KAAK;EAChB;EACA,OAAOF,YAAY,CAAC,CAAC,GAAGpB,MAAM,CAAC,EAAE,CAAC,GAAGC,MAAM,CAAC,EAAEC,OAAO,EAAEO,KAAK,CAAC;AACjE;AAEAnB,OAAO,CAACS,WAAW,GAAGA,WAAW;AACjCT,OAAO,CAACiC,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}