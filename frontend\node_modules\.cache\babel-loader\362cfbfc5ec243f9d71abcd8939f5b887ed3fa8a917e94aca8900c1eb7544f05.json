{"ast": null, "code": "export var pickAxisId = (_state, _axisType, axisId) => axisId;", "map": {"version": 3, "names": ["pickAxisId", "_state", "_axisType", "axisId"], "sources": ["D:/Desktop/meror/frontend/node_modules/recharts/es6/state/selectors/pickAxisId.js"], "sourcesContent": ["export var pickAxisId = (_state, _axisType, axisId) => axisId;"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAGA,CAACC,MAAM,EAAEC,SAAS,EAAEC,MAAM,KAAKA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}