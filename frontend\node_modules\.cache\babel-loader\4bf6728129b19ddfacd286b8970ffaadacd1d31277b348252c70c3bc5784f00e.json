{"ast": null, "code": "import { tickStep } from \"d3-array\";\nimport { format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound } from \"d3-format\";\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n    precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\":\n      {\n        var value = Math.max(Math.abs(start), Math.abs(stop));\n        if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n        return formatPrefix(specifier, value);\n      }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\":\n      {\n        if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n        break;\n      }\n    case \"f\":\n    case \"%\":\n      {\n        if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n        break;\n      }\n  }\n  return format(specifier);\n}", "map": {"version": 3, "names": ["tickStep", "format", "formatPrefix", "formatSpecifier", "precisionFixed", "precisionPrefix", "precisionRound", "tickFormat", "start", "stop", "count", "specifier", "step", "precision", "type", "value", "Math", "max", "abs", "isNaN"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-scale/src/tickFormat.js"], "sourcesContent": ["import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,UAAU;AACjC,SAAQC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAEC,cAAc,EAAEC,eAAe,EAAEC,cAAc,QAAO,WAAW;AAEhH,eAAe,SAASC,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAChE,IAAIC,IAAI,GAAGZ,QAAQ,CAACQ,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IACnCG,SAAS;EACbF,SAAS,GAAGR,eAAe,CAACQ,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,CAAC;EACjE,QAAQA,SAAS,CAACG,IAAI;IACpB,KAAK,GAAG;MAAE;QACR,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACV,KAAK,CAAC,EAAEQ,IAAI,CAACE,GAAG,CAACT,IAAI,CAAC,CAAC;QACrD,IAAIE,SAAS,CAACE,SAAS,IAAI,IAAI,IAAI,CAACM,KAAK,CAACN,SAAS,GAAGR,eAAe,CAACO,IAAI,EAAEG,KAAK,CAAC,CAAC,EAAEJ,SAAS,CAACE,SAAS,GAAGA,SAAS;QACpH,OAAOX,YAAY,CAACS,SAAS,EAAEI,KAAK,CAAC;MACvC;IACA,KAAK,EAAE;IACP,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,GAAG;MAAE;QACR,IAAIJ,SAAS,CAACE,SAAS,IAAI,IAAI,IAAI,CAACM,KAAK,CAACN,SAAS,GAAGP,cAAc,CAACM,IAAI,EAAEI,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACV,KAAK,CAAC,EAAEQ,IAAI,CAACE,GAAG,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEE,SAAS,CAACE,SAAS,GAAGA,SAAS,IAAIF,SAAS,CAACG,IAAI,KAAK,GAAG,CAAC;QAClL;MACF;IACA,KAAK,GAAG;IACR,KAAK,GAAG;MAAE;QACR,IAAIH,SAAS,CAACE,SAAS,IAAI,IAAI,IAAI,CAACM,KAAK,CAACN,SAAS,GAAGT,cAAc,CAACQ,IAAI,CAAC,CAAC,EAAED,SAAS,CAACE,SAAS,GAAGA,SAAS,GAAG,CAACF,SAAS,CAACG,IAAI,KAAK,GAAG,IAAI,CAAC;QAC3I;MACF;EACF;EACA,OAAOb,MAAM,CAACU,SAAS,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}