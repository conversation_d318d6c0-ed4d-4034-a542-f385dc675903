{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction getTag(value) {\n  if (value == null) {\n    return value === undefined ? '[object Undefined]' : '[object Null]';\n  }\n  return Object.prototype.toString.call(value);\n}\nexports.getTag = getTag;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "getTag", "undefined", "prototype", "toString", "call"], "sources": ["D:/Desktop/meror/frontend/node_modules/es-toolkit/dist/compat/_internal/getTag.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n\nexports.getTag = getTag;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,MAAMA,CAACD,KAAK,EAAE;EACnB,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOA,KAAK,KAAKE,SAAS,GAAG,oBAAoB,GAAG,eAAe;EACvE;EACA,OAAOP,MAAM,CAACQ,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC;AAChD;AAEAH,OAAO,CAACI,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}