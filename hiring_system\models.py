from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime

class ApplicationStatus(Enum):
    PENDING = "pending"
    SCREENING = "screening"
    INTERVIEW = "interview"
    REJECTED = "rejected"
    HIRED = "hired"

class Role(Enum):
    FRONTEND_ENGINEER = "frontend_engineer"
    BACKEND_ENGINEER = "backend_engineer"
    FULLSTACK_ENGINEER = "fullstack_engineer"
    DATA_SCIENTIST = "data_scientist"
    PRODUCT_MANAGER = "product_manager"
    ENGINEERING_MANAGER = "engineering_manager"
    DESIGNER = "designer"
    LEGAL_COUNSEL = "legal_counsel"
    FINANCIAL_ANALYST = "financial_analyst"
    BUSINESS_ANALYST = "business_analyst"

@dataclass
class WorkExperience:
    company: str
    role_name: str

@dataclass
class Education:
    degree: str
    subject: str
    school: str
    gpa: str
    start_date: str
    end_date: str
    original_school: str
    is_top50: bool = False
    is_top25: bool = False

@dataclass
class SalaryExpectation:
    full_time: Optional[str] = None
    part_time: Optional[str] = None

@dataclass
class CandidateScore:
    total: float = 0.0
    experience: float = 0.0
    education: float = 0.0
    skills: float = 0.0
    salary_fit: float = 0.0
    role_fit: float = 0.0
    diversity_bonus: float = 0.0

@dataclass
class Candidate:
    name: str
    email: str
    phone: str
    location: str
    submitted_at: str
    work_availability: List[str]
    annual_salary_expectation: SalaryExpectation
    work_experiences: List[WorkExperience]
    education: Dict[str, Any]
    skills: List[str]

    # Computed fields
    id: Optional[str] = None
    status: ApplicationStatus = ApplicationStatus.PENDING
    score: CandidateScore = field(default_factory=CandidateScore)
    suggested_role: Optional[Role] = None
    years_experience: int = 0
    highest_education_level: str = ""
    is_top_tier_education: bool = False

    def __post_init__(self):
        if self.id is None:
            self.id = self.email.replace("@", "_").replace(".", "_")

        # Calculate years of experience
        self.years_experience = len(self.work_experiences)

        # Extract highest education level
        if isinstance(self.education, dict) and "highest_level" in self.education:
            self.highest_education_level = self.education["highest_level"]

            # Check for top-tier education
            if "degrees" in self.education:
                for degree in self.education["degrees"]:
                    if isinstance(degree, dict):
                        if degree.get("isTop50", False) or degree.get("isTop25", False):
                            self.is_top_tier_education = True
                            break

@dataclass
class Team:
    id: str
    name: str
    members: List[Candidate]
    total_salary_cost: float
    diversity_score: float
    skill_coverage: Dict[str, int]
    geographic_distribution: Dict[str, int]
    justification: str
    created_at: datetime = field(default_factory=datetime.now)