from dataclasses import dataclass
from typing import List, Optional
from enum import Enum

class ApplicationStatus(Enum):
    PENDING = "pending"
    SCREENING = "screening"
    INTERVIEW = "interview"
    REJECTED = "rejected"
    HIRED = "hired"

@dataclass
class Candidate:
    id: str
    name: str
    email: str
    linkedin_url: Optional[str]
    resume_text: str
    years_experience: int
    skills: List[str]
    status: ApplicationStatus = ApplicationStatus.PENDING
    score: Optional[float] = None