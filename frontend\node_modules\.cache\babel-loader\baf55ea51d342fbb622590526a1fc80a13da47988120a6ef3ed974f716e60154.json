{"ast": null, "code": "function noop() {}\nconst localStorageManager = ({\n  key,\n  storageWindow\n}) => {\n  if (!storageWindow && typeof window !== 'undefined') {\n    storageWindow = window;\n  }\n  return {\n    get(defaultValue) {\n      if (typeof window === 'undefined') {\n        return undefined;\n      }\n      if (!storageWindow) {\n        return defaultValue;\n      }\n      let value;\n      try {\n        value = storageWindow.localStorage.getItem(key);\n      } catch {\n        // Unsupported\n      }\n      return value || defaultValue;\n    },\n    set: value => {\n      if (storageWindow) {\n        try {\n          storageWindow.localStorage.setItem(key, value);\n        } catch {\n          // Unsupported\n        }\n      }\n    },\n    subscribe: handler => {\n      if (!storageWindow) {\n        return noop;\n      }\n      const listener = event => {\n        const value = event.newValue;\n        if (event.key === key) {\n          handler(value);\n        }\n      };\n      storageWindow.addEventListener('storage', listener);\n      return () => {\n        storageWindow.removeEventListener('storage', listener);\n      };\n    }\n  };\n};\nexport default localStorageManager;", "map": {"version": 3, "names": ["noop", "localStorageManager", "key", "storageWindow", "window", "get", "defaultValue", "undefined", "value", "localStorage", "getItem", "set", "setItem", "subscribe", "handler", "listener", "event", "newValue", "addEventListener", "removeEventListener"], "sources": ["D:/Desktop/meror/frontend/node_modules/@mui/system/esm/cssVars/localStorageManager.js"], "sourcesContent": ["function noop() {}\nconst localStorageManager = ({\n  key,\n  storageWindow\n}) => {\n  if (!storageWindow && typeof window !== 'undefined') {\n    storageWindow = window;\n  }\n  return {\n    get(defaultValue) {\n      if (typeof window === 'undefined') {\n        return undefined;\n      }\n      if (!storageWindow) {\n        return defaultValue;\n      }\n      let value;\n      try {\n        value = storageWindow.localStorage.getItem(key);\n      } catch {\n        // Unsupported\n      }\n      return value || defaultValue;\n    },\n    set: value => {\n      if (storageWindow) {\n        try {\n          storageWindow.localStorage.setItem(key, value);\n        } catch {\n          // Unsupported\n        }\n      }\n    },\n    subscribe: handler => {\n      if (!storageWindow) {\n        return noop;\n      }\n      const listener = event => {\n        const value = event.newValue;\n        if (event.key === key) {\n          handler(value);\n        }\n      };\n      storageWindow.addEventListener('storage', listener);\n      return () => {\n        storageWindow.removeEventListener('storage', listener);\n      };\n    }\n  };\n};\nexport default localStorageManager;"], "mappings": "AAAA,SAASA,IAAIA,CAAA,EAAG,CAAC;AACjB,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,GAAG;EACHC;AACF,CAAC,KAAK;EACJ,IAAI,CAACA,aAAa,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACnDD,aAAa,GAAGC,MAAM;EACxB;EACA,OAAO;IACLC,GAAGA,CAACC,YAAY,EAAE;MAChB,IAAI,OAAOF,MAAM,KAAK,WAAW,EAAE;QACjC,OAAOG,SAAS;MAClB;MACA,IAAI,CAACJ,aAAa,EAAE;QAClB,OAAOG,YAAY;MACrB;MACA,IAAIE,KAAK;MACT,IAAI;QACFA,KAAK,GAAGL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACR,GAAG,CAAC;MACjD,CAAC,CAAC,MAAM;QACN;MAAA;MAEF,OAAOM,KAAK,IAAIF,YAAY;IAC9B,CAAC;IACDK,GAAG,EAAEH,KAAK,IAAI;MACZ,IAAIL,aAAa,EAAE;QACjB,IAAI;UACFA,aAAa,CAACM,YAAY,CAACG,OAAO,CAACV,GAAG,EAAEM,KAAK,CAAC;QAChD,CAAC,CAAC,MAAM;UACN;QAAA;MAEJ;IACF,CAAC;IACDK,SAAS,EAAEC,OAAO,IAAI;MACpB,IAAI,CAACX,aAAa,EAAE;QAClB,OAAOH,IAAI;MACb;MACA,MAAMe,QAAQ,GAAGC,KAAK,IAAI;QACxB,MAAMR,KAAK,GAAGQ,KAAK,CAACC,QAAQ;QAC5B,IAAID,KAAK,CAACd,GAAG,KAAKA,GAAG,EAAE;UACrBY,OAAO,CAACN,KAAK,CAAC;QAChB;MACF,CAAC;MACDL,aAAa,CAACe,gBAAgB,CAAC,SAAS,EAAEH,QAAQ,CAAC;MACnD,OAAO,MAAM;QACXZ,aAAa,CAACgB,mBAAmB,CAAC,SAAS,EAAEJ,QAAQ,CAAC;MACxD,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAed,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}