{"ast": null, "code": "// src/utils/react.ts\nimport * as React from \"react\";\n\n// src/utils/react-is.ts\nvar IS_REACT_19 = /* @__PURE__ */React.version.startsWith(\"19\");\nvar REACT_ELEMENT_TYPE = /* @__PURE__ */Symbol.for(IS_REACT_19 ? \"react.transitional.element\" : \"react.element\");\nvar REACT_PORTAL_TYPE = /* @__PURE__ */Symbol.for(\"react.portal\");\nvar REACT_FRAGMENT_TYPE = /* @__PURE__ */Symbol.for(\"react.fragment\");\nvar REACT_STRICT_MODE_TYPE = /* @__PURE__ */Symbol.for(\"react.strict_mode\");\nvar REACT_PROFILER_TYPE = /* @__PURE__ */Symbol.for(\"react.profiler\");\nvar REACT_CONSUMER_TYPE = /* @__PURE__ */Symbol.for(\"react.consumer\");\nvar REACT_CONTEXT_TYPE = /* @__PURE__ */Symbol.for(\"react.context\");\nvar REACT_FORWARD_REF_TYPE = /* @__PURE__ */Symbol.for(\"react.forward_ref\");\nvar REACT_SUSPENSE_TYPE = /* @__PURE__ */Symbol.for(\"react.suspense\");\nvar REACT_SUSPENSE_LIST_TYPE = /* @__PURE__ */Symbol.for(\"react.suspense_list\");\nvar REACT_MEMO_TYPE = /* @__PURE__ */Symbol.for(\"react.memo\");\nvar REACT_LAZY_TYPE = /* @__PURE__ */Symbol.for(\"react.lazy\");\nvar REACT_OFFSCREEN_TYPE = /* @__PURE__ */Symbol.for(\"react.offscreen\");\nvar REACT_CLIENT_REFERENCE = /* @__PURE__ */Symbol.for(\"react.client.reference\");\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nfunction isValidElementType(type) {\n  return typeof type === \"string\" || typeof type === \"function\" || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_OFFSCREEN_TYPE || typeof type === \"object\" && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE || type.getModuleId !== void 0) ? true : false;\n}\nfunction typeOf(object) {\n  if (typeof object === \"object\" && object !== null) {\n    const {\n      $$typeof\n    } = object;\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        switch (object = object.type, object) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n            return object;\n          default:\n            switch (object = object && object.$$typeof, object) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n                return object;\n              case REACT_CONSUMER_TYPE:\n                return object;\n              default:\n                return $$typeof;\n            }\n        }\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n}\nfunction isContextConsumer(object) {\n  return IS_REACT_19 ? typeOf(object) === REACT_CONSUMER_TYPE : typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\n\n// src/utils/warning.ts\nfunction warning(message) {\n  if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n    console.error(message);\n  }\n  try {\n    throw new Error(message);\n  } catch (e) {}\n}\n\n// src/connect/verifySubselectors.ts\nfunction verify(selector, methodName) {\n  if (!selector) {\n    throw new Error(`Unexpected value for ${methodName} in connect.`);\n  } else if (methodName === \"mapStateToProps\" || methodName === \"mapDispatchToProps\") {\n    if (!Object.prototype.hasOwnProperty.call(selector, \"dependsOnOwnProps\")) {\n      warning(`The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`);\n    }\n  }\n}\nfunction verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps) {\n  verify(mapStateToProps, \"mapStateToProps\");\n  verify(mapDispatchToProps, \"mapDispatchToProps\");\n  verify(mergeProps, \"mergeProps\");\n}\n\n// src/connect/selectorFactory.ts\nfunction pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, {\n  areStatesEqual,\n  areOwnPropsEqual,\n  areStatePropsEqual\n}) {\n  let hasRunAtLeastOnce = false;\n  let state;\n  let ownProps;\n  let stateProps;\n  let dispatchProps;\n  let mergedProps;\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleNewState() {\n    const nextStateProps = mapStateToProps(state, ownProps);\n    const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    const stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n}\nfunction finalPropsSelectorFactory(dispatch, {\n  initMapStateToProps,\n  initMapDispatchToProps,\n  initMergeProps,\n  ...options\n}) {\n  const mapStateToProps = initMapStateToProps(dispatch, options);\n  const mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  const mergeProps = initMergeProps(dispatch, options);\n  if (process.env.NODE_ENV !== \"production\") {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps);\n  }\n  return pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}\n\n// src/utils/bindActionCreators.ts\nfunction bindActionCreators(actionCreators, dispatch) {\n  const boundActionCreators = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === \"function\") {\n      boundActionCreators[key] = (...args) => dispatch(actionCreator(...args));\n    }\n  }\n  return boundActionCreators;\n}\n\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n  if (typeof obj !== \"object\" || obj === null) return false;\n  const proto = Object.getPrototypeOf(obj);\n  if (proto === null) return true;\n  let baseProto = proto;\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto);\n  }\n  return proto === baseProto;\n}\n\n// src/utils/verifyPlainObject.ts\nfunction verifyPlainObject(value, displayName, methodName) {\n  if (!isPlainObject(value)) {\n    warning(`${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`);\n  }\n}\n\n// src/connect/wrapMapToProps.ts\nfunction wrapMapToPropsConstant(getConstant) {\n  return function initConstantSelector(dispatch) {\n    const constant = getConstant(dispatch);\n    function constantSelector() {\n      return constant;\n    }\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n}\nfunction getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n}\nfunction wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, {\n    displayName\n  }) {\n    const proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch, void 0);\n    };\n    proxy.dependsOnOwnProps = true;\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      let props = proxy(stateOrDispatch, ownProps);\n      if (typeof props === \"function\") {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n      if (process.env.NODE_ENV !== \"production\") verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n    return proxy;\n  };\n}\n\n// src/connect/invalidArgFactory.ts\nfunction createInvalidArgFactory(arg, name) {\n  return (dispatch, options) => {\n    throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n  };\n}\n\n// src/connect/mapDispatchToProps.ts\nfunction mapDispatchToPropsFactory(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === \"object\" ? wrapMapToPropsConstant(dispatch =>\n  // @ts-ignore\n  bindActionCreators(mapDispatchToProps, dispatch)) : !mapDispatchToProps ? wrapMapToPropsConstant(dispatch => ({\n    dispatch\n  })) : typeof mapDispatchToProps === \"function\" ?\n  // @ts-ignore\n  wrapMapToPropsFunc(mapDispatchToProps, \"mapDispatchToProps\") : createInvalidArgFactory(mapDispatchToProps, \"mapDispatchToProps\");\n}\n\n// src/connect/mapStateToProps.ts\nfunction mapStateToPropsFactory(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(() => ({})) : typeof mapStateToProps === \"function\" ?\n  // @ts-ignore\n  wrapMapToPropsFunc(mapStateToProps, \"mapStateToProps\") : createInvalidArgFactory(mapStateToProps, \"mapStateToProps\");\n}\n\n// src/connect/mergeProps.ts\nfunction defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  return {\n    ...ownProps,\n    ...stateProps,\n    ...dispatchProps\n  };\n}\nfunction wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, {\n    displayName,\n    areMergedPropsEqual\n  }) {\n    let hasRunOnce = false;\n    let mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n      if (hasRunOnce) {\n        if (!areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== \"production\") verifyPlainObject(mergedProps, displayName, \"mergeProps\");\n      }\n      return mergedProps;\n    };\n  };\n}\nfunction mergePropsFactory(mergeProps) {\n  return !mergeProps ? () => defaultMergeProps : typeof mergeProps === \"function\" ? wrapMergePropsFunc(mergeProps) : createInvalidArgFactory(mergeProps, \"mergeProps\");\n}\n\n// src/utils/batch.ts\nfunction defaultNoopBatch(callback) {\n  callback();\n}\n\n// src/utils/Subscription.ts\nfunction createListenerCollection() {\n  let first = null;\n  let last = null;\n  return {\n    clear() {\n      first = null;\n      last = null;\n    },\n    notify() {\n      defaultNoopBatch(() => {\n        let listener = first;\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get() {\n      const listeners = [];\n      let listener = first;\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n      return listeners;\n    },\n    subscribe(callback) {\n      let isSubscribed = true;\n      const listener = last = {\n        callback,\n        next: null,\n        prev: last\n      };\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\nvar nullListeners = {\n  notify() {},\n  get: () => []\n};\nfunction createSubscription(store, parentSub) {\n  let unsubscribe;\n  let listeners = nullListeners;\n  let subscriptionsAmount = 0;\n  let selfSubscribed = false;\n  function addNestedSub(listener) {\n    trySubscribe();\n    const cleanupListener = listeners.subscribe(listener);\n    let removed = false;\n    return () => {\n      if (!removed) {\n        removed = true;\n        cleanupListener();\n        tryUnsubscribe();\n      }\n    };\n  }\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n  function isSubscribed() {\n    return selfSubscribed;\n  }\n  function trySubscribe() {\n    subscriptionsAmount++;\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n  function tryUnsubscribe() {\n    subscriptionsAmount--;\n    if (unsubscribe && subscriptionsAmount === 0) {\n      unsubscribe();\n      unsubscribe = void 0;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n  function trySubscribeSelf() {\n    if (!selfSubscribed) {\n      selfSubscribed = true;\n      trySubscribe();\n    }\n  }\n  function tryUnsubscribeSelf() {\n    if (selfSubscribed) {\n      selfSubscribed = false;\n      tryUnsubscribe();\n    }\n  }\n  const subscription = {\n    addNestedSub,\n    notifyNestedSubs,\n    handleChangeWrapper,\n    isSubscribed,\n    trySubscribe: trySubscribeSelf,\n    tryUnsubscribe: tryUnsubscribeSelf,\n    getListeners: () => listeners\n  };\n  return subscription;\n}\n\n// src/utils/useIsomorphicLayoutEffect.ts\nvar canUseDOM = () => !!(typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\");\nvar isDOM = /* @__PURE__ */canUseDOM();\nvar isRunningInReactNative = () => typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\";\nvar isReactNative = /* @__PURE__ */isRunningInReactNative();\nvar getUseIsomorphicLayoutEffect = () => isDOM || isReactNative ? React.useLayoutEffect : React.useEffect;\nvar useIsomorphicLayoutEffect = /* @__PURE__ */getUseIsomorphicLayoutEffect();\n\n// src/utils/shallowEqual.ts\nfunction is(x, y) {\n  if (x === y) {\n    return x !== 0 || y !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nfunction shallowEqual(objA, objB) {\n  if (is(objA, objB)) return true;\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n  for (let i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/utils/hoistStatics.ts\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {\n  [ForwardRef]: FORWARD_REF_STATICS,\n  [Memo]: MEMO_STATICS\n};\nfunction getStatics(component) {\n  if (isMemo(component)) {\n    return MEMO_STATICS;\n  }\n  return TYPE_STATICS[component[\"$$typeof\"]] || REACT_STATICS;\n}\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent) {\n  if (typeof sourceComponent !== \"string\") {\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent);\n      }\n    }\n    let keys = getOwnPropertyNames(sourceComponent);\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n    const targetStatics = getStatics(targetComponent);\n    const sourceStatics = getStatics(sourceComponent);\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i];\n      if (!KNOWN_STATICS[key] && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n        try {\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n  return targetComponent;\n}\n\n// src/components/Context.ts\nvar ContextKey = /* @__PURE__ */Symbol.for(`react-redux-context`);\nvar gT = typeof globalThis !== \"undefined\" ? globalThis : (/* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */\n{});\nfunction getContext() {\n  if (!React.createContext) return {};\n  const contextMap = gT[ContextKey] ??= /* @__PURE__ */new Map();\n  let realContext = contextMap.get(React.createContext);\n  if (!realContext) {\n    realContext = React.createContext(null);\n    if (process.env.NODE_ENV !== \"production\") {\n      realContext.displayName = \"ReactRedux\";\n    }\n    contextMap.set(React.createContext, realContext);\n  }\n  return realContext;\n}\nvar ReactReduxContext = /* @__PURE__ */getContext();\n\n// src/components/connect.tsx\nvar NO_SUBSCRIPTION_ARRAY = [null, null];\nvar stringifyComponent = Comp => {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(() => effectFunc(...effectArgs), dependencies);\n}\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n  lastWrapperProps.current = wrapperProps;\n  renderIsScheduled.current = false;\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n}\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, additionalSubscribeListener) {\n  if (!shouldHandleStateChanges) return () => {};\n  let didUnsubscribe = false;\n  let lastThrownError = null;\n  const checkForUpdates = () => {\n    if (didUnsubscribe || !isMounted.current) {\n      return;\n    }\n    const latestStoreState = store.getState();\n    let newChildProps, error;\n    try {\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n    if (!error) {\n      lastThrownError = null;\n    }\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true;\n      additionalSubscribeListener();\n    }\n  };\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe();\n  checkForUpdates();\n  const unsubscribeWrapper = () => {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n    if (lastThrownError) {\n      throw lastThrownError;\n    }\n  };\n  return unsubscribeWrapper;\n}\nfunction strictEqual(a, b) {\n  return a === b;\n}\nvar hasWarnedAboutDeprecatedPureOption = false;\nfunction connect(mapStateToProps, mapDispatchToProps, mergeProps, {\n  // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n  // @ts-ignore\n  pure,\n  areStatesEqual = strictEqual,\n  areOwnPropsEqual = shallowEqual,\n  areStatePropsEqual = shallowEqual,\n  areMergedPropsEqual = shallowEqual,\n  // use React's forwardRef to expose a ref of the wrapped component\n  forwardRef = false,\n  // the context consumer to use\n  context = ReactReduxContext\n} = {}) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (pure !== void 0 && !hasWarnedAboutDeprecatedPureOption) {\n      hasWarnedAboutDeprecatedPureOption = true;\n      warning('The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component');\n    }\n  }\n  const Context = context;\n  const initMapStateToProps = mapStateToPropsFactory(mapStateToProps);\n  const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps);\n  const initMergeProps = mergePropsFactory(mergeProps);\n  const shouldHandleStateChanges = Boolean(mapStateToProps);\n  const wrapWithConnect = WrappedComponent => {\n    if (process.env.NODE_ENV !== \"production\") {\n      const isValid = /* @__PURE__ */isValidElementType(WrappedComponent);\n      if (!isValid) throw new Error(`You must pass a component to the function returned by connect. Instead received ${stringifyComponent(WrappedComponent)}`);\n    }\n    const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n    const displayName = `Connect(${wrappedComponentName})`;\n    const selectorFactoryOptions = {\n      shouldHandleStateChanges,\n      displayName,\n      wrappedComponentName,\n      WrappedComponent,\n      // @ts-ignore\n      initMapStateToProps,\n      initMapDispatchToProps,\n      initMergeProps,\n      areStatesEqual,\n      areStatePropsEqual,\n      areOwnPropsEqual,\n      areMergedPropsEqual\n    };\n    function ConnectFunction(props) {\n      const [propsContext, reactReduxForwardedRef, wrapperProps] = React.useMemo(() => {\n        const {\n          reactReduxForwardedRef: reactReduxForwardedRef2,\n          ...wrapperProps2\n        } = props;\n        return [props.context, reactReduxForwardedRef2, wrapperProps2];\n      }, [props]);\n      const ContextToUse = React.useMemo(() => {\n        let ResultContext = Context;\n        if (propsContext?.Consumer) {\n          if (process.env.NODE_ENV !== \"production\") {\n            const isValid = /* @__PURE__ */isContextConsumer(\n            // @ts-ignore\n            /* @__PURE__ */\n            React.createElement(propsContext.Consumer, null));\n            if (!isValid) {\n              throw new Error(\"You must pass a valid React context consumer as `props.context`\");\n            }\n            ResultContext = propsContext;\n          }\n        }\n        return ResultContext;\n      }, [propsContext, Context]);\n      const contextValue = React.useContext(ContextToUse);\n      const didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      const didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n      if (process.env.NODE_ENV !== \"production\" && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(`Could not find \"store\" in the context of \"${displayName}\". Either wrap the root component in a <Provider>, or pass a custom React context provider to <Provider> and the corresponding React context consumer to ${displayName} in connect options.`);\n      }\n      const store = didStoreComeFromProps ? props.store : contextValue.store;\n      const getServerState = didStoreComeFromContext ? contextValue.getServerState : store.getState;\n      const childPropsSelector = React.useMemo(() => {\n        return finalPropsSelectorFactory(store.dispatch, selectorFactoryOptions);\n      }, [store]);\n      const [subscription, notifyNestedSubs] = React.useMemo(() => {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY;\n        const subscription2 = createSubscription(store, didStoreComeFromProps ? void 0 : contextValue.subscription);\n        const notifyNestedSubs2 = subscription2.notifyNestedSubs.bind(subscription2);\n        return [subscription2, notifyNestedSubs2];\n      }, [store, didStoreComeFromProps, contextValue]);\n      const overriddenContextValue = React.useMemo(() => {\n        if (didStoreComeFromProps) {\n          return contextValue;\n        }\n        return {\n          ...contextValue,\n          subscription\n        };\n      }, [didStoreComeFromProps, contextValue, subscription]);\n      const lastChildProps = React.useRef(void 0);\n      const lastWrapperProps = React.useRef(wrapperProps);\n      const childPropsFromStoreUpdate = React.useRef(void 0);\n      const renderIsScheduled = React.useRef(false);\n      const isMounted = React.useRef(false);\n      const latestSubscriptionCallbackError = React.useRef(void 0);\n      useIsomorphicLayoutEffect(() => {\n        isMounted.current = true;\n        return () => {\n          isMounted.current = false;\n        };\n      }, []);\n      const actualChildPropsSelector = React.useMemo(() => {\n        const selector = () => {\n          if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n            return childPropsFromStoreUpdate.current;\n          }\n          return childPropsSelector(store.getState(), wrapperProps);\n        };\n        return selector;\n      }, [store, wrapperProps]);\n      const subscribeForReact = React.useMemo(() => {\n        const subscribe = reactListener => {\n          if (!subscription) {\n            return () => {};\n          }\n          return subscribeUpdates(shouldHandleStateChanges, store, subscription,\n          // @ts-ignore\n          childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, reactListener);\n        };\n        return subscribe;\n      }, [subscription]);\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs]);\n      let actualChildProps;\n      try {\n        actualChildProps = React.useSyncExternalStore(\n        // TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n        subscribeForReact,\n        // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n        // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n        actualChildPropsSelector, getServerState ? () => childPropsSelector(getServerState(), wrapperProps) : actualChildPropsSelector);\n      } catch (err) {\n        if (latestSubscriptionCallbackError.current) {\n          ;\n          err.message += `\nThe error may be correlated with this previous error:\n${latestSubscriptionCallbackError.current.stack}\n\n`;\n        }\n        throw err;\n      }\n      useIsomorphicLayoutEffect(() => {\n        latestSubscriptionCallbackError.current = void 0;\n        childPropsFromStoreUpdate.current = void 0;\n        lastChildProps.current = actualChildProps;\n      });\n      const renderedWrappedComponent = React.useMemo(() => {\n        return (\n          // @ts-ignore\n          /* @__PURE__ */\n          React.createElement(WrappedComponent, {\n            ...actualChildProps,\n            ref: reactReduxForwardedRef\n          })\n        );\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]);\n      const renderedChild = React.useMemo(() => {\n        if (shouldHandleStateChanges) {\n          return /* @__PURE__ */React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    }\n    const _Connect = React.memo(ConnectFunction);\n    const Connect = _Connect;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n    if (forwardRef) {\n      const _forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        return /* @__PURE__ */React.createElement(Connect, {\n          ...props,\n          reactReduxForwardedRef: ref\n        });\n      });\n      const forwarded = _forwarded;\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return /* @__PURE__ */hoistNonReactStatics(forwarded, WrappedComponent);\n    }\n    return /* @__PURE__ */hoistNonReactStatics(Connect, WrappedComponent);\n  };\n  return wrapWithConnect;\n}\nvar connect_default = connect;\n\n// src/components/Provider.tsx\nfunction Provider(providerProps) {\n  const {\n    children,\n    context,\n    serverState,\n    store\n  } = providerProps;\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store);\n    const baseContextValue = {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : void 0\n    };\n    if (process.env.NODE_ENV === \"production\") {\n      return baseContextValue;\n    } else {\n      const {\n        identityFunctionCheck = \"once\",\n        stabilityCheck = \"once\"\n      } = providerProps;\n      return /* @__PURE__ */Object.assign(baseContextValue, {\n        stabilityCheck,\n        identityFunctionCheck\n      });\n    }\n  }, [store, serverState]);\n  const previousState = React.useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = void 0;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext;\n  return /* @__PURE__ */React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\nvar Provider_default = Provider;\n\n// src/hooks/useReduxContext.ts\nfunction createReduxContextHook(context = ReactReduxContext) {\n  return function useReduxContext2() {\n    const contextValue = React.useContext(context);\n    if (process.env.NODE_ENV !== \"production\" && !contextValue) {\n      throw new Error(\"could not find react-redux context value; please ensure the component is wrapped in a <Provider>\");\n    }\n    return contextValue;\n  };\n}\nvar useReduxContext = /* @__PURE__ */createReduxContextHook();\n\n// src/hooks/useStore.ts\nfunction createStoreHook(context = ReactReduxContext) {\n  const useReduxContext2 = context === ReactReduxContext ? useReduxContext :\n  // @ts-ignore\n  createReduxContextHook(context);\n  const useStore2 = () => {\n    const {\n      store\n    } = useReduxContext2();\n    return store;\n  };\n  Object.assign(useStore2, {\n    withTypes: () => useStore2\n  });\n  return useStore2;\n}\nvar useStore = /* @__PURE__ */createStoreHook();\n\n// src/hooks/useDispatch.ts\nfunction createDispatchHook(context = ReactReduxContext) {\n  const useStore2 = context === ReactReduxContext ? useStore : createStoreHook(context);\n  const useDispatch2 = () => {\n    const store = useStore2();\n    return store.dispatch;\n  };\n  Object.assign(useDispatch2, {\n    withTypes: () => useDispatch2\n  });\n  return useDispatch2;\n}\nvar useDispatch = /* @__PURE__ */createDispatchHook();\n\n// src/hooks/useSelector.ts\nimport { useSyncExternalStoreWithSelector } from \"use-sync-external-store/with-selector.js\";\nvar refEquality = (a, b) => a === b;\nfunction createSelectorHook(context = ReactReduxContext) {\n  const useReduxContext2 = context === ReactReduxContext ? useReduxContext : createReduxContextHook(context);\n  const useSelector2 = (selector, equalityFnOrOptions = {}) => {\n    const {\n      equalityFn = refEquality\n    } = typeof equalityFnOrOptions === \"function\" ? {\n      equalityFn: equalityFnOrOptions\n    } : equalityFnOrOptions;\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!selector) {\n        throw new Error(`You must pass a selector to useSelector`);\n      }\n      if (typeof selector !== \"function\") {\n        throw new Error(`You must pass a function as a selector to useSelector`);\n      }\n      if (typeof equalityFn !== \"function\") {\n        throw new Error(`You must pass a function as an equality function to useSelector`);\n      }\n    }\n    const reduxContext = useReduxContext2();\n    const {\n      store,\n      subscription,\n      getServerState\n    } = reduxContext;\n    const firstRun = React.useRef(true);\n    const wrappedSelector = React.useCallback({\n      [selector.name](state) {\n        const selected = selector(state);\n        if (process.env.NODE_ENV !== \"production\") {\n          const {\n            devModeChecks = {}\n          } = typeof equalityFnOrOptions === \"function\" ? {} : equalityFnOrOptions;\n          const {\n            identityFunctionCheck,\n            stabilityCheck\n          } = reduxContext;\n          const {\n            identityFunctionCheck: finalIdentityFunctionCheck,\n            stabilityCheck: finalStabilityCheck\n          } = {\n            stabilityCheck,\n            identityFunctionCheck,\n            ...devModeChecks\n          };\n          if (finalStabilityCheck === \"always\" || finalStabilityCheck === \"once\" && firstRun.current) {\n            const toCompare = selector(state);\n            if (!equalityFn(selected, toCompare)) {\n              let stack = void 0;\n              try {\n                throw new Error();\n              } catch (e) {\n                ;\n                ({\n                  stack\n                } = e);\n              }\n              console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned a different result when called with the same parameters. This can lead to unnecessary rerenders.\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization\", {\n                state,\n                selected,\n                selected2: toCompare,\n                stack\n              });\n            }\n          }\n          if (finalIdentityFunctionCheck === \"always\" || finalIdentityFunctionCheck === \"once\" && firstRun.current) {\n            if (selected === state) {\n              let stack = void 0;\n              try {\n                throw new Error();\n              } catch (e) {\n                ;\n                ({\n                  stack\n                } = e);\n              }\n              console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned the root state when called. This can lead to unnecessary rerenders.\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.\", {\n                stack\n              });\n            }\n          }\n          if (firstRun.current) firstRun.current = false;\n        }\n        return selected;\n      }\n    }[selector.name], [selector]);\n    const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, wrappedSelector, equalityFn);\n    React.useDebugValue(selectedState);\n    return selectedState;\n  };\n  Object.assign(useSelector2, {\n    withTypes: () => useSelector2\n  });\n  return useSelector2;\n}\nvar useSelector = /* @__PURE__ */createSelectorHook();\n\n// src/exports.ts\nvar batch = defaultNoopBatch;\nexport { Provider_default as Provider, ReactReduxContext, batch, connect_default as connect, createDispatchHook, createSelectorHook, createStoreHook, shallowEqual, useDispatch, useSelector, useStore };", "map": {"version": 3, "names": ["React", "IS_REACT_19", "version", "startsWith", "REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_CONSUMER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_CLIENT_REFERENCE", "ForwardRef", "Memo", "isValidElementType", "type", "$$typeof", "getModuleId", "typeOf", "object", "isContextConsumer", "isMemo", "warning", "message", "console", "error", "Error", "e", "verify", "selector", "methodName", "Object", "prototype", "hasOwnProperty", "call", "verifySubselectors", "mapStateToProps", "mapDispatchToProps", "mergeProps", "pureFinalPropsSelectorFactory", "dispatch", "areStatesEqual", "areOwnPropsEqual", "areStatePropsEqual", "hasRunAtLeastOnce", "state", "ownProps", "stateProps", "dispatchProps", "mergedProps", "handleFirstCall", "firstState", "firstOwnProps", "handleNewPropsAndNewState", "dependsOnOwnProps", "handleNewProps", "handleNewState", "nextStateProps", "statePropsChanged", "handleSubsequentCalls", "nextState", "nextOwnProps", "propsChanged", "stateChanged", "pureFinalPropsSelector", "finalPropsSelectorFactory", "initMapStateToProps", "initMapDispatchToProps", "initMergeProps", "options", "process", "env", "NODE_ENV", "bindActionCreators", "actionCreators", "boundActionCreators", "key", "actionCreator", "args", "isPlainObject", "obj", "proto", "getPrototypeOf", "baseProto", "verifyPlainObject", "value", "displayName", "wrapMapToPropsConstant", "getConstant", "initConstantSelector", "constant", "constantSelector", "getDependsOnOwnProps", "mapToProps", "Boolean", "length", "wrapMapToPropsFunc", "initProxySelector", "proxy", "mapToPropsProxy", "stateOrDispatch", "detectFactoryAndVerify", "props", "createInvalidArgFactory", "arg", "name", "wrappedComponentName", "mapDispatchToPropsFactory", "mapStateToPropsFactory", "defaultMergeProps", "wrapMergePropsFunc", "initMergePropsProxy", "areMergedPropsEqual", "hasRunOnce", "mergePropsProxy", "nextMergedProps", "mergePropsFactory", "defaultNoopBatch", "callback", "createListenerCollection", "first", "last", "clear", "notify", "listener", "next", "get", "listeners", "push", "subscribe", "isSubscribed", "prev", "unsubscribe", "nullListeners", "createSubscription", "store", "parentSub", "subscriptionsAmount", "selfSubscribed", "addNestedSub", "trySubscribe", "cleanupListener", "removed", "tryUnsubscribe", "notifyNestedSubs", "handleChangeWrapper", "subscription", "onStateChange", "trySubscribeSelf", "tryUnsubscribeSelf", "getListeners", "canUseDOM", "window", "document", "createElement", "isDOM", "isRunningInReactNative", "navigator", "product", "isReactNative", "getUseIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useIsomorphicLayoutEffect", "is", "x", "y", "shallowEqual", "objA", "objB", "keysA", "keys", "keysB", "i", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "defaultProps", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "KNOWN_STATICS", "caller", "callee", "arguments", "arity", "FORWARD_REF_STATICS", "render", "MEMO_STATICS", "compare", "TYPE_STATICS", "getStatics", "component", "defineProperty", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "inheritedComponent", "concat", "targetStatics", "sourceStatics", "descriptor", "Context<PERSON>ey", "gT", "globalThis", "getContext", "createContext", "contextMap", "Map", "realContext", "set", "ReactReduxContext", "NO_SUBSCRIPTION_ARRAY", "stringifyComponent", "Comp", "JSON", "stringify", "err", "String", "useIsomorphicLayoutEffectWithArgs", "effectFunc", "effectArgs", "dependencies", "captureWrapperProps", "lastWrapperProps", "lastChildProps", "renderIsScheduled", "wrapperProps", "childPropsFromStoreUpdate", "current", "subscribeUpdates", "shouldHandleStateChanges", "childPropsSelector", "isMounted", "additionalSubscribeListener", "didUnsubscribe", "lastThrownError", "checkForUpdates", "latestStoreState", "getState", "newChildProps", "unsubscribeWrapper", "strictEqual", "a", "b", "hasWarnedAboutDeprecatedPureOption", "connect", "pure", "forwardRef", "context", "Context", "wrapWithConnect", "WrappedComponent", "<PERSON><PERSON><PERSON><PERSON>", "selectorFactoryOptions", "ConnectFunction", "props<PERSON><PERSON><PERSON><PERSON>", "reactReduxForwardedRef", "useMemo", "reactReduxForwardedRef2", "wrapperProps2", "ContextToUse", "ResultContext", "Consumer", "contextValue", "useContext", "didStoreComeFromProps", "didStoreComeFromContext", "getServerState", "subscription2", "notifyNestedSubs2", "bind", "overriddenContextValue", "useRef", "latestSubscriptionCallbackError", "actualChildPropsSelector", "subscribeForReact", "reactListener", "actualChildProps", "useSyncExternalStore", "stack", "renderedWrappedComponent", "ref", "<PERSON><PERSON><PERSON><PERSON>", "Provider", "_Connect", "memo", "Connect", "_forwarded", "forwardConnectRef", "forwarded", "connect_default", "providerProps", "children", "serverState", "baseContextValue", "identityFunctionCheck", "stabilityCheck", "assign", "previousState", "Provider_default", "createReduxContextHook", "useReduxContext2", "useReduxContext", "createStoreHook", "useStore2", "useStore", "withTypes", "createDispatchHook", "useDispatch2", "useDispatch", "useSyncExternalStoreWithSelector", "refEquality", "createSelectorHook", "useSelector2", "useSelector", "equalityFnOrOptions", "equalityFn", "reduxContext", "firstRun", "wrappedSelector", "useCallback", "selected", "dev<PERSON>ode<PERSON><PERSON><PERSON>", "finalIdentityFunctionCheck", "finalStabilityCheck", "toCompare", "warn", "selected2", "selectedState", "useDebugValue", "batch"], "sources": ["D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\react.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\react-is.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\warning.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\verifySubselectors.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\selectorFactory.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\bindActionCreators.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\isPlainObject.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\verifyPlainObject.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\wrapMapToProps.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\invalidArgFactory.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\mapDispatchToProps.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\mapStateToProps.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\connect\\mergeProps.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\batch.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\Subscription.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\useIsomorphicLayoutEffect.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\shallowEqual.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\utils\\hoistStatics.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\components\\Context.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\components\\connect.tsx", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\components\\Provider.tsx", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\hooks\\useReduxContext.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\hooks\\useStore.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\hooks\\useDispatch.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\hooks\\useSelector.ts", "D:\\Desktop\\meror\\frontend\\node_modules\\react-redux\\src\\exports.ts"], "sourcesContent": ["import * as React from 'react'\n\nexport { React }\n", "import type { ElementType, MemoExoticComponent, ReactElement } from 'react'\nimport { React } from './react'\n\n// Directly ported from:\n// https://unpkg.com/browse/react-is@19.0.0/cjs/react-is.production.js\n// It's very possible this could change in the future, but given that\n// we only use these in `connect`, this is a low priority.\n\nexport const IS_REACT_19 = /* @__PURE__ */ React.version.startsWith('19')\n\nconst REACT_ELEMENT_TYPE = /* @__PURE__ */ Symbol.for(\n  IS_REACT_19 ? 'react.transitional.element' : 'react.element',\n)\nconst REACT_PORTAL_TYPE = /* @__PURE__ */ Symbol.for('react.portal')\nconst REACT_FRAGMENT_TYPE = /* @__PURE__ */ Symbol.for('react.fragment')\nconst REACT_STRICT_MODE_TYPE = /* @__PURE__ */ Symbol.for('react.strict_mode')\nconst REACT_PROFILER_TYPE = /* @__PURE__ */ Symbol.for('react.profiler')\nconst REACT_CONSUMER_TYPE = /* @__PURE__ */ Symbol.for('react.consumer')\nconst REACT_CONTEXT_TYPE = /* @__PURE__ */ Symbol.for('react.context')\nconst REACT_FORWARD_REF_TYPE = /* @__PURE__ */ Symbol.for('react.forward_ref')\nconst REACT_SUSPENSE_TYPE = /* @__PURE__ */ Symbol.for('react.suspense')\nconst REACT_SUSPENSE_LIST_TYPE = /* @__PURE__ */ Symbol.for(\n  'react.suspense_list',\n)\nconst REACT_MEMO_TYPE = /* @__PURE__ */ Symbol.for('react.memo')\nconst REACT_LAZY_TYPE = /* @__PURE__ */ Symbol.for('react.lazy')\nconst REACT_OFFSCREEN_TYPE = /* @__PURE__ */ Symbol.for('react.offscreen')\nconst REACT_CLIENT_REFERENCE = /* @__PURE__ */ Symbol.for(\n  'react.client.reference',\n)\n\nexport const ForwardRef = REACT_FORWARD_REF_TYPE\nexport const Memo = REACT_MEMO_TYPE\n\nexport function isValidElementType(type: any): type is ElementType {\n  return typeof type === 'string' ||\n    typeof type === 'function' ||\n    type === REACT_FRAGMENT_TYPE ||\n    type === REACT_PROFILER_TYPE ||\n    type === REACT_STRICT_MODE_TYPE ||\n    type === REACT_SUSPENSE_TYPE ||\n    type === REACT_SUSPENSE_LIST_TYPE ||\n    type === REACT_OFFSCREEN_TYPE ||\n    (typeof type === 'object' &&\n      type !== null &&\n      (type.$$typeof === REACT_LAZY_TYPE ||\n        type.$$typeof === REACT_MEMO_TYPE ||\n        type.$$typeof === REACT_CONTEXT_TYPE ||\n        type.$$typeof === REACT_CONSUMER_TYPE ||\n        type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        type.$$typeof === REACT_CLIENT_REFERENCE ||\n        type.getModuleId !== undefined))\n    ? !0\n    : !1\n}\n\nfunction typeOf(object: any): symbol | undefined {\n  if (typeof object === 'object' && object !== null) {\n    const { $$typeof } = object\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        switch (((object = object.type), object)) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n            return object\n          default:\n            switch (((object = object && object.$$typeof), object)) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n                return object\n              case REACT_CONSUMER_TYPE:\n                return object\n              default:\n                return $$typeof\n            }\n        }\n      case REACT_PORTAL_TYPE:\n        return $$typeof\n    }\n  }\n}\n\nexport function isContextConsumer(object: any): object is ReactElement {\n  return IS_REACT_19\n    ? typeOf(object) === REACT_CONSUMER_TYPE\n    : typeOf(object) === REACT_CONTEXT_TYPE\n}\n\nexport function isMemo(object: any): object is MemoExoticComponent<any> {\n  return typeOf(object) === REACT_MEMO_TYPE\n}\n", "/**\r\n * Prints a warning in the console if it exists.\r\n *\r\n * @param {String} message The warning message.\r\n * @returns {void}\r\n */\r\nexport default function warning(message: string) {\r\n  /* eslint-disable no-console */\r\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\r\n    console.error(message)\r\n  }\r\n  /* eslint-enable no-console */\r\n  try {\r\n    // This error was thrown as a convenience so that if you enable\r\n    // \"break on all exceptions\" in your console,\r\n    // it would pause the execution at this line.\r\n    throw new Error(message)\r\n    /* eslint-disable no-empty */\r\n  } catch (e) {}\r\n  /* eslint-enable no-empty */\r\n}\r\n", "import warning from '../utils/warning'\n\nfunction verify(selector: unknown, methodName: string): void {\n  if (!selector) {\n    throw new Error(`Unexpected value for ${methodName} in connect.`)\n  } else if (\n    methodName === 'mapStateToProps' ||\n    methodName === 'mapDispatchToProps'\n  ) {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(\n        `The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`,\n      )\n    }\n  }\n}\n\nexport default function verifySubselectors(\n  mapStateToProps: unknown,\n  mapDispatchToProps: unknown,\n  mergeProps: unknown,\n): void {\n  verify(mapStateToProps, 'mapStateToProps')\n  verify(mapDispatchToProps, 'mapDispatchToProps')\n  verify(mergeProps, 'mergeProps')\n}\n", "import type { Dispatch, Action } from 'redux'\nimport type { ComponentType } from 'react'\nimport verifySubselectors from './verifySubselectors'\nimport type { EqualityFn, ExtendedEqualityFn } from '../types'\n\nexport type SelectorFactory<S, TProps, TOwnProps, TFactoryOptions> = (\n  dispatch: Dispatch<Action<string>>,\n  factoryOptions: TFactoryOptions,\n) => Selector<S, TProps, TOwnProps>\n\nexport type Selector<S, TProps, TOwnProps = null> = TOwnProps extends\n  | null\n  | undefined\n  ? (state: S) => TProps\n  : (state: S, ownProps: TOwnProps) => TProps\n\nexport type MapStateToProps<TStateProps, TOwnProps, State> = (\n  state: State,\n  ownProps: TOwnProps,\n) => TStateProps\n\nexport type MapStateToPropsFactory<TStateProps, TOwnProps, State> = (\n  initialState: State,\n  ownProps: TOwnProps,\n) => MapStateToProps<TStateProps, TOwnProps, State>\n\nexport type MapStateToPropsParam<TStateProps, TOwnProps, State> =\n  | MapStateToPropsFactory<TStateProps, TOwnProps, State>\n  | MapStateToProps<TStateProps, TOwnProps, State>\n  | null\n  | undefined\n\nexport type MapDispatchToPropsFunction<TDispatchProps, TOwnProps> = (\n  dispatch: Dispatch<Action<string>>,\n  ownProps: TOwnProps,\n) => TDispatchProps\n\nexport type MapDispatchToProps<TDispatchProps, TOwnProps> =\n  | MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\n  | TDispatchProps\n\nexport type MapDispatchToPropsFactory<TDispatchProps, TOwnProps> = (\n  dispatch: Dispatch<Action<string>>,\n  ownProps: TOwnProps,\n) => MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\n\nexport type MapDispatchToPropsParam<TDispatchProps, TOwnProps> =\n  | MapDispatchToPropsFactory<TDispatchProps, TOwnProps>\n  | MapDispatchToProps<TDispatchProps, TOwnProps>\n\nexport type MapDispatchToPropsNonObject<TDispatchProps, TOwnProps> =\n  | MapDispatchToPropsFactory<TDispatchProps, TOwnProps>\n  | MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\n\nexport type MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps> = (\n  stateProps: TStateProps,\n  dispatchProps: TDispatchProps,\n  ownProps: TOwnProps,\n) => TMergedProps\n\ninterface PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State> {\n  readonly areStatesEqual: ExtendedEqualityFn<State, TOwnProps>\n  readonly areStatePropsEqual: EqualityFn<TStateProps>\n  readonly areOwnPropsEqual: EqualityFn<TOwnProps>\n}\n\nfunction pureFinalPropsSelectorFactory<\n  TStateProps,\n  TOwnProps,\n  TDispatchProps,\n  TMergedProps,\n  State,\n>(\n  mapStateToProps: WrappedMapStateToProps<TStateProps, TOwnProps, State>,\n  mapDispatchToProps: WrappedMapDispatchToProps<TDispatchProps, TOwnProps>,\n  mergeProps: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n  dispatch: Dispatch<Action<string>>,\n  {\n    areStatesEqual,\n    areOwnPropsEqual,\n    areStatePropsEqual,\n  }: PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State>,\n) {\n  let hasRunAtLeastOnce = false\n  let state: State\n  let ownProps: TOwnProps\n  let stateProps: TStateProps\n  let dispatchProps: TDispatchProps\n  let mergedProps: TMergedProps\n\n  function handleFirstCall(firstState: State, firstOwnProps: TOwnProps) {\n    state = firstState\n    ownProps = firstOwnProps\n    stateProps = mapStateToProps(state, ownProps)\n    dispatchProps = mapDispatchToProps(dispatch, ownProps)\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n    hasRunAtLeastOnce = true\n    return mergedProps\n  }\n\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps)\n\n    if (mapDispatchToProps.dependsOnOwnProps)\n      dispatchProps = mapDispatchToProps(dispatch, ownProps)\n\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n    return mergedProps\n  }\n\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps)\n      stateProps = mapStateToProps(state, ownProps)\n\n    if (mapDispatchToProps.dependsOnOwnProps)\n      dispatchProps = mapDispatchToProps(dispatch, ownProps)\n\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n    return mergedProps\n  }\n\n  function handleNewState() {\n    const nextStateProps = mapStateToProps(state, ownProps)\n    const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps)\n    stateProps = nextStateProps\n\n    if (statePropsChanged)\n      mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n\n    return mergedProps\n  }\n\n  function handleSubsequentCalls(nextState: State, nextOwnProps: TOwnProps) {\n    const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps)\n    const stateChanged = !areStatesEqual(\n      nextState,\n      state,\n      nextOwnProps,\n      ownProps,\n    )\n    state = nextState\n    ownProps = nextOwnProps\n\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState()\n    if (propsChanged) return handleNewProps()\n    if (stateChanged) return handleNewState()\n    return mergedProps\n  }\n\n  return function pureFinalPropsSelector(\n    nextState: State,\n    nextOwnProps: TOwnProps,\n  ) {\n    return hasRunAtLeastOnce\n      ? handleSubsequentCalls(nextState, nextOwnProps)\n      : handleFirstCall(nextState, nextOwnProps)\n  }\n}\n\ninterface WrappedMapStateToProps<TStateProps, TOwnProps, State> {\n  (state: State, ownProps: TOwnProps): TStateProps\n  readonly dependsOnOwnProps: boolean\n}\n\ninterface WrappedMapDispatchToProps<TDispatchProps, TOwnProps> {\n  (dispatch: Dispatch<Action<string>>, ownProps: TOwnProps): TDispatchProps\n  readonly dependsOnOwnProps: boolean\n}\n\nexport interface InitOptions<TStateProps, TOwnProps, TMergedProps, State>\n  extends PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State> {\n  readonly shouldHandleStateChanges: boolean\n  readonly displayName: string\n  readonly wrappedComponentName: string\n  readonly WrappedComponent: ComponentType<TOwnProps>\n  readonly areMergedPropsEqual: EqualityFn<TMergedProps>\n}\n\nexport interface SelectorFactoryOptions<\n  TStateProps,\n  TOwnProps,\n  TDispatchProps,\n  TMergedProps,\n  State,\n> extends InitOptions<TStateProps, TOwnProps, TMergedProps, State> {\n  readonly initMapStateToProps: (\n    dispatch: Dispatch<Action<string>>,\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>,\n  ) => WrappedMapStateToProps<TStateProps, TOwnProps, State>\n  readonly initMapDispatchToProps: (\n    dispatch: Dispatch<Action<string>>,\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>,\n  ) => WrappedMapDispatchToProps<TDispatchProps, TOwnProps>\n  readonly initMergeProps: (\n    dispatch: Dispatch<Action<string>>,\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>,\n  ) => MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>\n}\n\n// TODO: Add more comments\n\n// The selector returned by selectorFactory will memoize its results,\n// allowing connect's shouldComponentUpdate to return false if final\n// props have not changed.\n\nexport default function finalPropsSelectorFactory<\n  TStateProps,\n  TOwnProps,\n  TDispatchProps,\n  TMergedProps,\n  State,\n>(\n  dispatch: Dispatch<Action<string>>,\n  {\n    initMapStateToProps,\n    initMapDispatchToProps,\n    initMergeProps,\n    ...options\n  }: SelectorFactoryOptions<\n    TStateProps,\n    TOwnProps,\n    TDispatchProps,\n    TMergedProps,\n    State\n  >,\n) {\n  const mapStateToProps = initMapStateToProps(dispatch, options)\n  const mapDispatchToProps = initMapDispatchToProps(dispatch, options)\n  const mergeProps = initMergeProps(dispatch, options)\n\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps)\n  }\n\n  return pureFinalPropsSelectorFactory<\n    TStateProps,\n    TOwnProps,\n    TDispatchProps,\n    TMergedProps,\n    State\n  >(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options)\n}\n", "import type { ActionCreatorsMapObject, Dispatch } from 'redux'\n\nexport default function bindActionCreators(\n  actionCreators: ActionCreatorsMapObject,\n  dispatch: Dispatch,\n): ActionCreatorsMapObject {\n  const boundActionCreators: ActionCreatorsMapObject = {}\n\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key]\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = (...args) => dispatch(actionCreator(...args))\n    }\n  }\n  return boundActionCreators\n}\n", "/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nexport default function isPlainObject(obj: unknown) {\n  if (typeof obj !== 'object' || obj === null) return false\n\n  const proto = Object.getPrototypeOf(obj)\n  if (proto === null) return true\n\n  let baseProto = proto\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto)\n  }\n\n  return proto === baseProto\n}\n", "import isPlainObject from './isPlainObject'\nimport warning from './warning'\n\nexport default function verifyPlainObject(\n  value: unknown,\n  displayName: string,\n  methodName: string,\n) {\n  if (!isPlainObject(value)) {\n    warning(\n      `${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`,\n    )\n  }\n}\n", "import type { ActionCreatorsMapObject, Dispatch, ActionCreator } from 'redux'\n\nimport type { FixTypeLater } from '../types'\nimport verifyPlainObject from '../utils/verifyPlainObject'\n\ntype AnyState = { [key: string]: any }\ntype StateOrDispatch<S extends AnyState = AnyState> = S | Dispatch\n\ntype AnyProps = { [key: string]: any }\n\nexport type MapToProps<P extends AnyProps = AnyProps> = {\n  // eslint-disable-next-line no-unused-vars\n  (stateOrDispatch: StateOrDispatch, ownProps?: P): FixTypeLater\n  dependsOnOwnProps?: boolean\n}\n\nexport function wrapMapToPropsConstant(\n  // * Note:\n  //  It seems that the dispatch argument\n  //  could be a dispatch function in some cases (ex: whenMapDispatchToPropsIsMissing)\n  //  and a state object in some others (ex: whenMapStateToPropsIsMissing)\n  // eslint-disable-next-line no-unused-vars\n  getConstant: (dispatch: Dispatch) =>\n    | {\n        dispatch?: Dispatch\n        dependsOnOwnProps?: boolean\n      }\n    | ActionCreatorsMapObject\n    | ActionCreator<any>,\n) {\n  return function initConstantSelector(dispatch: Dispatch) {\n    const constant = getConstant(dispatch)\n\n    function constantSelector() {\n      return constant\n    }\n    constantSelector.dependsOnOwnProps = false\n    return constantSelector\n  }\n}\n\n// dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n// TODO Can this get pulled out so that we can subscribe directly to the store if we don't need ownProps?\nfunction getDependsOnOwnProps(mapToProps: MapToProps) {\n  return mapToProps.dependsOnOwnProps\n    ? Boolean(mapToProps.dependsOnOwnProps)\n    : mapToProps.length !== 1\n}\n\n// Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\nexport function wrapMapToPropsFunc<P extends AnyProps = AnyProps>(\n  mapToProps: MapToProps,\n  methodName: string,\n) {\n  return function initProxySelector(\n    dispatch: Dispatch,\n    { displayName }: { displayName: string },\n  ) {\n    const proxy = function mapToPropsProxy(\n      stateOrDispatch: StateOrDispatch,\n      ownProps?: P,\n    ): MapToProps {\n      return proxy.dependsOnOwnProps\n        ? proxy.mapToProps(stateOrDispatch, ownProps)\n        : proxy.mapToProps(stateOrDispatch, undefined)\n    }\n\n    // allow detectFactoryAndVerify to get ownProps\n    proxy.dependsOnOwnProps = true\n\n    proxy.mapToProps = function detectFactoryAndVerify(\n      stateOrDispatch: StateOrDispatch,\n      ownProps?: P,\n    ): MapToProps {\n      proxy.mapToProps = mapToProps\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps)\n      let props = proxy(stateOrDispatch, ownProps)\n\n      if (typeof props === 'function') {\n        proxy.mapToProps = props\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props)\n        props = proxy(stateOrDispatch, ownProps)\n      }\n\n      if (process.env.NODE_ENV !== 'production')\n        verifyPlainObject(props, displayName, methodName)\n\n      return props\n    }\n\n    return proxy\n  }\n}\n", "import type { Action, Dispatch } from 'redux'\n\nexport function createInvalidArgFactory(arg: unknown, name: string) {\n  return (\n    dispatch: Dispatch<Action<string>>,\n    options: { readonly wrappedComponentName: string },\n  ) => {\n    throw new Error(\n      `Invalid value of type ${typeof arg} for ${name} argument when connecting component ${\n        options.wrappedComponentName\n      }.`,\n    )\n  }\n}\n", "import type { Action, Dispatch } from 'redux'\nimport bindActionCreators from '../utils/bindActionCreators'\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps'\nimport { createInvalidArgFactory } from './invalidArgFactory'\nimport type { MapDispatchToPropsParam } from './selectorFactory'\n\nexport function mapDispatchToPropsFactory<TDispatchProps, TOwnProps>(\n  mapDispatchToProps:\n    | MapDispatchToPropsParam<TDispatchProps, TOwnProps>\n    | undefined,\n) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object'\n    ? wrapMapToPropsConstant((dispatch: Dispatch<Action<string>>) =>\n        // @ts-ignore\n        bindActionCreators(mapDispatchToProps, dispatch),\n      )\n    : !mapDispatchToProps\n      ? wrapMapToPropsConstant((dispatch: Dispatch<Action<string>>) => ({\n          dispatch,\n        }))\n      : typeof mapDispatchToProps === 'function'\n        ? // @ts-ignore\n          wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps')\n        : createInvalidArgFactory(mapDispatchToProps, 'mapDispatchToProps')\n}\n", "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps'\nimport { createInvalidArgFactory } from './invalidArgFactory'\nimport type { MapStateToPropsParam } from './selectorFactory'\n\nexport function mapStateToPropsFactory<TStateProps, TOwnProps, State>(\n  mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n) {\n  return !mapStateToProps\n    ? wrapMapToPropsConstant(() => ({}))\n    : typeof mapStateToProps === 'function'\n      ? // @ts-ignore\n        wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps')\n      : createInvalidArgFactory(mapStateToProps, 'mapStateToProps')\n}\n", "import type { Action, Dispatch } from 'redux'\nimport verifyPlainObject from '../utils/verifyPlainObject'\nimport { createInvalidArgFactory } from './invalidArgFactory'\nimport type { MergeProps } from './selectorFactory'\nimport type { EqualityFn } from '../types'\n\nfunction defaultMergeProps<\n  TStateProps,\n  TDispatchProps,\n  TOwnProps,\n  TMergedProps,\n>(\n  stateProps: TStateProps,\n  dispatchProps: TDispatchProps,\n  ownProps: TOwnProps,\n): TMergedProps {\n  // @ts-ignore\n  return { ...ownProps, ...stateProps, ...dispatchProps }\n}\n\nfunction wrapMergePropsFunc<\n  TStateProps,\n  TDispatchProps,\n  TOwnProps,\n  TMergedProps,\n>(\n  mergeProps: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n): (\n  dispatch: Dispatch<Action<string>>,\n  options: {\n    readonly displayName: string\n    readonly areMergedPropsEqual: EqualityFn<TMergedProps>\n  },\n) => MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps> {\n  return function initMergePropsProxy(\n    dispatch,\n    { displayName, areMergedPropsEqual },\n  ) {\n    let hasRunOnce = false\n    let mergedProps: TMergedProps\n\n    return function mergePropsProxy(\n      stateProps: TStateProps,\n      dispatchProps: TDispatchProps,\n      ownProps: TOwnProps,\n    ) {\n      const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n\n      if (hasRunOnce) {\n        if (!areMergedPropsEqual(nextMergedProps, mergedProps))\n          mergedProps = nextMergedProps\n      } else {\n        hasRunOnce = true\n        mergedProps = nextMergedProps\n\n        if (process.env.NODE_ENV !== 'production')\n          verifyPlainObject(mergedProps, displayName, 'mergeProps')\n      }\n\n      return mergedProps\n    }\n  }\n}\n\nexport function mergePropsFactory<\n  TStateProps,\n  TDispatchProps,\n  TOwnProps,\n  TMergedProps,\n>(\n  mergeProps?: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n) {\n  return !mergeProps\n    ? () => defaultMergeProps\n    : typeof mergeProps === 'function'\n      ? wrapMergePropsFunc(mergeProps)\n      : createInvalidArgFactory(mergeProps, 'mergeProps')\n}\n", "// Default to a dummy \"batch\" implementation that just runs the callback\r\nexport function defaultNoopBatch(callback: () => void) {\r\n  callback()\r\n}\r\n", "import { defaultNoopBatch as batch } from './batch'\n\n// encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\ntype VoidFunc = () => void\n\ntype Listener = {\n  callback: VoidFunc\n  next: Listener | null\n  prev: Listener | null\n}\n\nfunction createListenerCollection() {\n  let first: Listener | null = null\n  let last: Listener | null = null\n\n  return {\n    clear() {\n      first = null\n      last = null\n    },\n\n    notify() {\n      batch(() => {\n        let listener = first\n        while (listener) {\n          listener.callback()\n          listener = listener.next\n        }\n      })\n    },\n\n    get() {\n      const listeners: Listener[] = []\n      let listener = first\n      while (listener) {\n        listeners.push(listener)\n        listener = listener.next\n      }\n      return listeners\n    },\n\n    subscribe(callback: () => void) {\n      let isSubscribed = true\n\n      const listener: Listener = (last = {\n        callback,\n        next: null,\n        prev: last,\n      })\n\n      if (listener.prev) {\n        listener.prev.next = listener\n      } else {\n        first = listener\n      }\n\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return\n        isSubscribed = false\n\n        if (listener.next) {\n          listener.next.prev = listener.prev\n        } else {\n          last = listener.prev\n        }\n        if (listener.prev) {\n          listener.prev.next = listener.next\n        } else {\n          first = listener.next\n        }\n      }\n    },\n  }\n}\n\ntype ListenerCollection = ReturnType<typeof createListenerCollection>\n\nexport interface Subscription {\n  addNestedSub: (listener: VoidFunc) => VoidFunc\n  notifyNestedSubs: VoidFunc\n  handleChangeWrapper: VoidFunc\n  isSubscribed: () => boolean\n  onStateChange?: VoidFunc | null\n  trySubscribe: VoidFunc\n  tryUnsubscribe: VoidFunc\n  getListeners: () => ListenerCollection\n}\n\nconst nullListeners = {\n  notify() {},\n  get: () => [],\n} as unknown as ListenerCollection\n\nexport function createSubscription(store: any, parentSub?: Subscription) {\n  let unsubscribe: VoidFunc | undefined\n  let listeners: ListenerCollection = nullListeners\n\n  // Reasons to keep the subscription active\n  let subscriptionsAmount = 0\n\n  // Is this specific subscription subscribed (or only nested ones?)\n  let selfSubscribed = false\n\n  function addNestedSub(listener: () => void) {\n    trySubscribe()\n\n    const cleanupListener = listeners.subscribe(listener)\n\n    // cleanup nested sub\n    let removed = false\n    return () => {\n      if (!removed) {\n        removed = true\n        cleanupListener()\n        tryUnsubscribe()\n      }\n    }\n  }\n\n  function notifyNestedSubs() {\n    listeners.notify()\n  }\n\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange()\n    }\n  }\n\n  function isSubscribed() {\n    return selfSubscribed\n  }\n\n  function trySubscribe() {\n    subscriptionsAmount++\n    if (!unsubscribe) {\n      unsubscribe = parentSub\n        ? parentSub.addNestedSub(handleChangeWrapper)\n        : store.subscribe(handleChangeWrapper)\n\n      listeners = createListenerCollection()\n    }\n  }\n\n  function tryUnsubscribe() {\n    subscriptionsAmount--\n    if (unsubscribe && subscriptionsAmount === 0) {\n      unsubscribe()\n      unsubscribe = undefined\n      listeners.clear()\n      listeners = nullListeners\n    }\n  }\n\n  function trySubscribeSelf() {\n    if (!selfSubscribed) {\n      selfSubscribed = true\n      trySubscribe()\n    }\n  }\n\n  function tryUnsubscribeSelf() {\n    if (selfSubscribed) {\n      selfSubscribed = false\n      tryUnsubscribe()\n    }\n  }\n\n  const subscription: Subscription = {\n    addNestedSub,\n    notifyNestedSubs,\n    handleChangeWrapper,\n    isSubscribed,\n    trySubscribe: trySubscribeSelf,\n    tryUnsubscribe: tryUnsubscribeSelf,\n    getListeners: () => listeners,\n  }\n\n  return subscription\n}\n", "import { React } from '../utils/react'\n\n// <PERSON>act currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n\n// Matches logic in React's `shared/ExecutionEnvironment` file\nconst canUseDOM = () =>\n  !!(\n    typeof window !== 'undefined' &&\n    typeof window.document !== 'undefined' &&\n    typeof window.document.createElement !== 'undefined'\n  )\n\nconst isDOM = /* @__PURE__ */ canUseDOM()\n\n// Under React Native, we know that we always want to use useLayoutEffect\n\n/**\n * Checks if the code is running in a React Native environment.\n *\n * @returns Whether the code is running in a React Native environment.\n *\n * @see {@link https://github.com/facebook/react-native/issues/1331 Reference}\n */\nconst isRunningInReactNative = () =>\n  typeof navigator !== 'undefined' && navigator.product === 'ReactNative'\n\nconst isReactNative = /* @__PURE__ */ isRunningInReactNative()\n\nconst getUseIsomorphicLayoutEffect = () =>\n  isDOM || isReactNative ? React.useLayoutEffect : React.useEffect\n\nexport const useIsomorphicLayoutEffect =\n  /* @__PURE__ */ getUseIsomorphicLayoutEffect()\n", "function is(x: unknown, y: unknown) {\r\n  if (x === y) {\r\n    return x !== 0 || y !== 0 || 1 / x === 1 / y\r\n  } else {\r\n    return x !== x && y !== y\r\n  }\r\n}\r\n\r\nexport default function shallowEqual(objA: any, objB: any) {\r\n  if (is(objA, objB)) return true\r\n\r\n  if (\r\n    typeof objA !== 'object' ||\r\n    objA === null ||\r\n    typeof objB !== 'object' ||\r\n    objB === null\r\n  ) {\r\n    return false\r\n  }\r\n\r\n  const keysA = Object.keys(objA)\r\n  const keysB = Object.keys(objB)\r\n\r\n  if (keysA.length !== keysB.length) return false\r\n\r\n  for (let i = 0; i < keysA.length; i++) {\r\n    if (\r\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i]) ||\r\n      !is(objA[keysA[i]], objB[keysA[i]])\r\n    ) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  return true\r\n}\r\n", "// Copied directly from:\n// https://github.com/mridgway/hoist-non-react-statics/blob/main/src/index.js\n// https://unpkg.com/browse/@types/hoist-non-react-statics@3.3.6/index.d.ts\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nimport type { ForwardRefExoticComponent, MemoExoticComponent } from 'react'\nimport { ForwardRef, Memo, isMemo } from '../utils/react-is'\n\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n} as const\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n} as const\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n} as const\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n} as const\n\nconst TYPE_STATICS = {\n  [ForwardRef]: FORWARD_REF_STATICS,\n  [Memo]: MEMO_STATICS,\n} as const\n\nfunction getStatics(component: any) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS\n  }\n\n  // React v16.12 and above\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS\n}\n\nexport type NonReactStatics<\n  Source,\n  C extends {\n    [key: string]: true\n  } = {},\n> = {\n  [key in Exclude<\n    keyof Source,\n    Source extends MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : Source extends ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: Source[key]\n}\n\nconst defineProperty = Object.defineProperty\nconst getOwnPropertyNames = Object.getOwnPropertyNames\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor\nconst getPrototypeOf = Object.getPrototypeOf\nconst objectPrototype = Object.prototype\n\nexport default function hoistNonReactStatics<\n  Target,\n  Source,\n  CustomStatic extends {\n    [key: string]: true\n  } = {},\n>(\n  targetComponent: Target,\n  sourceComponent: Source,\n): Target & NonReactStatics<Source, CustomStatic> {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent)\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent)\n      }\n    }\n\n    let keys: (string | symbol)[] = getOwnPropertyNames(sourceComponent)\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent))\n    }\n\n    const targetStatics = getStatics(targetComponent)\n    const sourceStatics = getStatics(sourceComponent)\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i]\n      if (\n        !KNOWN_STATICS[key as keyof typeof KNOWN_STATICS] &&\n        !(sourceStatics && sourceStatics[key as keyof typeof sourceStatics]) &&\n        !(targetStatics && targetStatics[key as keyof typeof targetStatics])\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key)\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!)\n        } catch (e) {\n          // ignore\n        }\n      }\n    }\n  }\n\n  return targetComponent as any\n}\n", "import type { Context } from 'react'\nimport { React } from '../utils/react'\nimport type { Action, Store, UnknownAction } from 'redux'\nimport type { Subscription } from '../utils/Subscription'\nimport type { ProviderProps } from './Provider'\n\nexport interface ReactReduxContextValue<\n  SS = any,\n  A extends Action<string> = UnknownAction,\n> extends Pick<ProviderProps, 'stabilityCheck' | 'identityFunctionCheck'> {\n  store: Store<SS, A>\n  subscription: Subscription\n  getServerState?: () => SS\n}\n\nconst ContextKey = /* @__PURE__ */ Symbol.for(`react-redux-context`)\nconst gT: {\n  [ContextKey]?: Map<\n    typeof React.createContext,\n    Context<ReactReduxContextValue | null>\n  >\n} = (\n  typeof globalThis !== 'undefined'\n    ? globalThis\n    : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */ {}\n) as any\n\nfunction getContext(): Context<ReactReduxContextValue | null> {\n  if (!React.createContext) return {} as any\n\n  const contextMap = (gT[ContextKey] ??= new Map<\n    typeof React.createContext,\n    Context<ReactReduxContextValue | null>\n  >())\n  let realContext = contextMap.get(React.createContext)\n  if (!realContext) {\n    realContext = React.createContext<ReactReduxContextValue | null>(\n      null as any,\n    )\n    if (process.env.NODE_ENV !== 'production') {\n      realContext.displayName = 'ReactRedux'\n    }\n    contextMap.set(React.createContext, realContext)\n  }\n  return realContext\n}\n\nexport const ReactReduxContext = /*#__PURE__*/ getContext()\n\nexport type ReactReduxContextInstance = typeof ReactReduxContext\n", "/* eslint-disable valid-jsdoc, @typescript-eslint/no-unused-vars */\nimport type { ComponentType } from 'react'\nimport { React } from '../utils/react'\nimport { isValidElementType, isContextConsumer } from '../utils/react-is'\n\nimport type { Store } from 'redux'\n\nimport type {\n  ConnectedComponent,\n  InferableComponentEnhancer,\n  InferableComponentEnhancerWithProps,\n  ResolveThunks,\n  DispatchProp,\n  ConnectPropsMaybeWithoutContext,\n} from '../types'\n\nimport type {\n  MapStateToPropsParam,\n  MapDispatchToPropsParam,\n  MergeProps,\n  MapDispatchToPropsNonObject,\n  SelectorFactoryOptions,\n} from '../connect/selectorFactory'\nimport defaultSelectorFactory from '../connect/selectorFactory'\nimport { mapDispatchToPropsFactory } from '../connect/mapDispatchToProps'\nimport { mapStateToPropsFactory } from '../connect/mapStateToProps'\nimport { mergePropsFactory } from '../connect/mergeProps'\n\nimport type { Subscription } from '../utils/Subscription'\nimport { createSubscription } from '../utils/Subscription'\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect'\nimport shallowEqual from '../utils/shallowEqual'\nimport hoistStatics from '../utils/hoistStatics'\nimport warning from '../utils/warning'\n\nimport type {\n  ReactReduxContextValue,\n  ReactReduxContextInstance,\n} from './Context'\nimport { ReactReduxContext } from './Context'\n\n// Define some constant arrays just to avoid re-creating these\nconst EMPTY_ARRAY: [unknown, number] = [null, 0]\nconst NO_SUBSCRIPTION_ARRAY = [null, null]\n\n// Attempts to stringify whatever not-really-a-component value we were given\n// for logging in an error message\nconst stringifyComponent = (Comp: unknown) => {\n  try {\n    return JSON.stringify(Comp)\n  } catch (err) {\n    return String(Comp)\n  }\n}\n\ntype EffectFunc = (...args: any[]) => void | ReturnType<React.EffectCallback>\n\n// This is \"just\" a `useLayoutEffect`, but with two modifications:\n// - we need to fall back to `useEffect` in SSR to avoid annoying warnings\n// - we extract this to a separate function to avoid closing over values\n//   and causing memory leaks\nfunction useIsomorphicLayoutEffectWithArgs(\n  effectFunc: EffectFunc,\n  effectArgs: any[],\n  dependencies?: React.DependencyList,\n) {\n  useIsomorphicLayoutEffect(() => effectFunc(...effectArgs), dependencies)\n}\n\n// Effect callback, extracted: assign the latest props values to refs for later usage\nfunction captureWrapperProps(\n  lastWrapperProps: React.MutableRefObject<unknown>,\n  lastChildProps: React.MutableRefObject<unknown>,\n  renderIsScheduled: React.MutableRefObject<boolean>,\n  wrapperProps: unknown,\n  // actualChildProps: unknown,\n  childPropsFromStoreUpdate: React.MutableRefObject<unknown>,\n  notifyNestedSubs: () => void,\n) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps\n  renderIsScheduled.current = false\n\n  // If the render was from a store update, clear out that reference and cascade the subscriber update\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null\n    notifyNestedSubs()\n  }\n}\n\n// Effect callback, extracted: subscribe to the Redux store or nearest connected ancestor,\n// check for updates after dispatched actions, and trigger re-renders.\nfunction subscribeUpdates(\n  shouldHandleStateChanges: boolean,\n  store: Store,\n  subscription: Subscription,\n  childPropsSelector: (state: unknown, props: unknown) => unknown,\n  lastWrapperProps: React.MutableRefObject<unknown>,\n  lastChildProps: React.MutableRefObject<unknown>,\n  renderIsScheduled: React.MutableRefObject<boolean>,\n  isMounted: React.MutableRefObject<boolean>,\n  childPropsFromStoreUpdate: React.MutableRefObject<unknown>,\n  notifyNestedSubs: () => void,\n  // forceComponentUpdateDispatch: React.Dispatch<any>,\n  additionalSubscribeListener: () => void,\n) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return () => {}\n\n  // Capture values for checking if and when this component unmounts\n  let didUnsubscribe = false\n  let lastThrownError: Error | null = null\n\n  // We'll run this callback every time a store subscription update propagates to this component\n  const checkForUpdates = () => {\n    if (didUnsubscribe || !isMounted.current) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return\n    }\n\n    // TODO We're currently calling getState ourselves here, rather than letting `uSES` do it\n    const latestStoreState = store.getState()\n\n    let newChildProps, error\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(\n        latestStoreState,\n        lastWrapperProps.current,\n      )\n    } catch (e) {\n      error = e\n      lastThrownError = e as Error | null\n    }\n\n    if (!error) {\n      lastThrownError = null\n    }\n\n    // If the child props haven't changed, nothing to do here - cascade the subscription update\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs()\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps\n      childPropsFromStoreUpdate.current = newChildProps\n      renderIsScheduled.current = true\n\n      // TODO This is hacky and not how `uSES` is meant to be used\n      // Trigger the React `useSyncExternalStore` subscriber\n      additionalSubscribeListener()\n    }\n  }\n\n  // Actually subscribe to the nearest connected ancestor (or store)\n  subscription.onStateChange = checkForUpdates\n  subscription.trySubscribe()\n\n  // Pull data from the store after first render in case the store has\n  // changed since we began.\n  checkForUpdates()\n\n  const unsubscribeWrapper = () => {\n    didUnsubscribe = true\n    subscription.tryUnsubscribe()\n    subscription.onStateChange = null\n\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError\n    }\n  }\n\n  return unsubscribeWrapper\n}\n\n// Reducer initial state creation for our update reducer\nconst initStateUpdates = () => EMPTY_ARRAY\n\nexport interface ConnectProps {\n  /** A custom Context instance that the component can use to access the store from an alternate Provider using that same Context instance */\n  context?: ReactReduxContextInstance\n  /** A Redux store instance to be used for subscriptions instead of the store from a Provider */\n  store?: Store\n}\n\ninterface InternalConnectProps extends ConnectProps {\n  reactReduxForwardedRef?: React.ForwardedRef<unknown>\n}\n\nfunction strictEqual(a: unknown, b: unknown) {\n  return a === b\n}\n\n/**\n * Infers the type of props that a connector will inject into a component.\n */\nexport type ConnectedProps<TConnector> =\n  TConnector extends InferableComponentEnhancerWithProps<\n    infer TInjectedProps,\n    any\n  >\n    ? unknown extends TInjectedProps\n      ? TConnector extends InferableComponentEnhancer<infer TInjectedProps>\n        ? TInjectedProps\n        : never\n      : TInjectedProps\n    : never\n\nexport interface ConnectOptions<\n  State = unknown,\n  TStateProps = {},\n  TOwnProps = {},\n  TMergedProps = {},\n> {\n  forwardRef?: boolean\n  context?: typeof ReactReduxContext\n  areStatesEqual?: (\n    nextState: State,\n    prevState: State,\n    nextOwnProps: TOwnProps,\n    prevOwnProps: TOwnProps,\n  ) => boolean\n\n  areOwnPropsEqual?: (\n    nextOwnProps: TOwnProps,\n    prevOwnProps: TOwnProps,\n  ) => boolean\n\n  areStatePropsEqual?: (\n    nextStateProps: TStateProps,\n    prevStateProps: TStateProps,\n  ) => boolean\n  areMergedPropsEqual?: (\n    nextMergedProps: TMergedProps,\n    prevMergedProps: TMergedProps,\n  ) => boolean\n}\n\n/**\n * Connects a React component to a Redux store.\n *\n * - Without arguments, just wraps the component, without changing the behavior / props\n *\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\n * is to override ownProps (as stated in the docs), so what remains is everything that's\n * not a state or dispatch prop\n *\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\n * should be valid component props, because it depends on mergeProps implementation.\n * As such, it is the user's responsibility to extend ownProps interface from state or\n * dispatch props or both when applicable\n *\n * @param mapStateToProps\n * @param mapDispatchToProps\n * @param mergeProps\n * @param options\n */\nexport interface Connect<DefaultState = unknown> {\n  // tslint:disable:no-unnecessary-generics\n  (): InferableComponentEnhancer<DispatchProp>\n\n  /** mapState only */\n  <TStateProps = {}, no_dispatch = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n  ): InferableComponentEnhancerWithProps<TStateProps & DispatchProp, TOwnProps>\n\n  /** mapDispatch only (as a function) */\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<TDispatchProps, TOwnProps>\n\n  /** mapDispatch only (as an object) */\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mapState and mapDispatch (as a function)*/\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & TDispatchProps,\n    TOwnProps\n  >\n\n  /** mapState and mapDispatch (nullish) */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: null | undefined,\n  ): InferableComponentEnhancerWithProps<TStateProps, TOwnProps>\n\n  /** mapState and mapDispatch (as an object) */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mergeProps only */\n  <no_state = {}, no_dispatch = {}, TOwnProps = {}, TMergedProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: null | undefined,\n    mergeProps: MergeProps<undefined, DispatchProp, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n\n  /** mapState and mergeProps */\n  <\n    TStateProps = {},\n    no_dispatch = {},\n    TOwnProps = {},\n    TMergedProps = {},\n    State = DefaultState,\n  >(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: null | undefined,\n    mergeProps: MergeProps<TStateProps, DispatchProp, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n\n  /** mapDispatch (as a object) and mergeProps */\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}, TMergedProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: MergeProps<undefined, TDispatchProps, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n\n  /** mapState and options */\n  <TStateProps = {}, no_dispatch = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: null | undefined,\n    mergeProps: null | undefined,\n    options: ConnectOptions<State, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<DispatchProp & TStateProps, TOwnProps>\n\n  /** mapDispatch (as a function) and options */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<{}, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<TDispatchProps, TOwnProps>\n\n  /** mapDispatch (as an object) and options*/\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<{}, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mapState,  mapDispatch (as a function), and options */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<State, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & TDispatchProps,\n    TOwnProps\n  >\n\n  /** mapState,  mapDispatch (as an object), and options */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<State, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mapState, mapDispatch, mergeProps, and options */\n  <\n    TStateProps = {},\n    TDispatchProps = {},\n    TOwnProps = {},\n    TMergedProps = {},\n    State = DefaultState,\n  >(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: MergeProps<\n      TStateProps,\n      TDispatchProps,\n      TOwnProps,\n      TMergedProps\n    >,\n    options?: ConnectOptions<State, TStateProps, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n  // tslint:enable:no-unnecessary-generics\n}\n\nlet hasWarnedAboutDeprecatedPureOption = false\n\n/**\n * Connects a React component to a Redux store.\n *\n * - Without arguments, just wraps the component, without changing the behavior / props\n *\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\n * is to override ownProps (as stated in the docs), so what remains is everything that's\n * not a state or dispatch prop\n *\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\n * should be valid component props, because it depends on mergeProps implementation.\n * As such, it is the user's responsibility to extend ownProps interface from state or\n * dispatch props or both when applicable\n *\n * @param mapStateToProps A function that extracts values from state\n * @param mapDispatchToProps Setup for dispatching actions\n * @param mergeProps Optional callback to merge state and dispatch props together\n * @param options Options for configuring the connection\n *\n */\nfunction connect<\n  TStateProps = {},\n  TDispatchProps = {},\n  TOwnProps = {},\n  TMergedProps = {},\n  State = unknown,\n>(\n  mapStateToProps?: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n  mapDispatchToProps?: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n  mergeProps?: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n  {\n    // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n    // @ts-ignore\n    pure,\n    areStatesEqual = strictEqual,\n    areOwnPropsEqual = shallowEqual,\n    areStatePropsEqual = shallowEqual,\n    areMergedPropsEqual = shallowEqual,\n\n    // use React's forwardRef to expose a ref of the wrapped component\n    forwardRef = false,\n\n    // the context consumer to use\n    context = ReactReduxContext,\n  }: ConnectOptions<unknown, unknown, unknown, unknown> = {},\n): unknown {\n  if (process.env.NODE_ENV !== 'production') {\n    if (pure !== undefined && !hasWarnedAboutDeprecatedPureOption) {\n      hasWarnedAboutDeprecatedPureOption = true\n      warning(\n        'The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component',\n      )\n    }\n  }\n\n  const Context = context\n\n  const initMapStateToProps = mapStateToPropsFactory(mapStateToProps)\n  const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps)\n  const initMergeProps = mergePropsFactory(mergeProps)\n\n  const shouldHandleStateChanges = Boolean(mapStateToProps)\n\n  const wrapWithConnect = <TProps,>(\n    WrappedComponent: ComponentType<TProps>,\n  ) => {\n    type WrappedComponentProps = TProps &\n      ConnectPropsMaybeWithoutContext<TProps>\n\n    if (process.env.NODE_ENV !== 'production') {\n      const isValid = /*#__PURE__*/ isValidElementType(WrappedComponent)\n      if (!isValid)\n        throw new Error(\n          `You must pass a component to the function returned by connect. Instead received ${stringifyComponent(\n            WrappedComponent,\n          )}`,\n        )\n    }\n\n    const wrappedComponentName =\n      WrappedComponent.displayName || WrappedComponent.name || 'Component'\n\n    const displayName = `Connect(${wrappedComponentName})`\n\n    const selectorFactoryOptions: SelectorFactoryOptions<\n      any,\n      any,\n      any,\n      any,\n      State\n    > = {\n      shouldHandleStateChanges,\n      displayName,\n      wrappedComponentName,\n      WrappedComponent,\n      // @ts-ignore\n      initMapStateToProps,\n      initMapDispatchToProps,\n      initMergeProps,\n      areStatesEqual,\n      areStatePropsEqual,\n      areOwnPropsEqual,\n      areMergedPropsEqual,\n    }\n\n    function ConnectFunction<TOwnProps>(\n      props: InternalConnectProps & TOwnProps,\n    ) {\n      const [propsContext, reactReduxForwardedRef, wrapperProps] =\n        React.useMemo(() => {\n          // Distinguish between actual \"data\" props that were passed to the wrapper component,\n          // and values needed to control behavior (forwarded refs, alternate context instances).\n          // To maintain the wrapperProps object reference, memoize this destructuring.\n          const { reactReduxForwardedRef, ...wrapperProps } = props\n          return [props.context, reactReduxForwardedRef, wrapperProps]\n        }, [props])\n\n      const ContextToUse: ReactReduxContextInstance = React.useMemo(() => {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        let ResultContext = Context\n        if (propsContext?.Consumer) {\n          if (process.env.NODE_ENV !== 'production') {\n            const isValid = /*#__PURE__*/ isContextConsumer(\n              // @ts-ignore\n              <propsContext.Consumer />,\n            )\n            if (!isValid) {\n              throw new Error(\n                'You must pass a valid React context consumer as `props.context`',\n              )\n            }\n            ResultContext = propsContext\n          }\n        }\n        return ResultContext\n      }, [propsContext, Context])\n\n      // Retrieve the store and ancestor subscription via context, if available\n      const contextValue = React.useContext(ContextToUse)\n\n      // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n      const didStoreComeFromProps =\n        Boolean(props.store) &&\n        Boolean(props.store!.getState) &&\n        Boolean(props.store!.dispatch)\n      const didStoreComeFromContext =\n        Boolean(contextValue) && Boolean(contextValue!.store)\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !didStoreComeFromProps &&\n        !didStoreComeFromContext\n      ) {\n        throw new Error(\n          `Could not find \"store\" in the context of ` +\n            `\"${displayName}\". Either wrap the root component in a <Provider>, ` +\n            `or pass a custom React context provider to <Provider> and the corresponding ` +\n            `React context consumer to ${displayName} in connect options.`,\n        )\n      }\n\n      // Based on the previous check, one of these must be true\n      const store: Store = didStoreComeFromProps\n        ? props.store!\n        : contextValue!.store\n\n      const getServerState = didStoreComeFromContext\n        ? contextValue!.getServerState\n        : store.getState\n\n      const childPropsSelector = React.useMemo(() => {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return defaultSelectorFactory(store.dispatch, selectorFactoryOptions)\n      }, [store])\n\n      const [subscription, notifyNestedSubs] = React.useMemo(() => {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY\n\n        // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n        const subscription = createSubscription(\n          store,\n          didStoreComeFromProps ? undefined : contextValue!.subscription,\n        )\n\n        // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n        const notifyNestedSubs =\n          subscription.notifyNestedSubs.bind(subscription)\n\n        return [subscription, notifyNestedSubs]\n      }, [store, didStoreComeFromProps, contextValue])\n\n      // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n      const overriddenContextValue = React.useMemo(() => {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue!\n        }\n\n        // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n        return {\n          ...contextValue,\n          subscription,\n        } as ReactReduxContextValue\n      }, [didStoreComeFromProps, contextValue, subscription])\n\n      // Set up refs to coordinate values between the subscription effect and the render logic\n      const lastChildProps = React.useRef<unknown>(undefined)\n      const lastWrapperProps = React.useRef(wrapperProps)\n      const childPropsFromStoreUpdate = React.useRef<unknown>(undefined)\n      const renderIsScheduled = React.useRef(false)\n      const isMounted = React.useRef(false)\n\n      // TODO: Change this to `React.useRef<Error>(undefined)` after upgrading to React 19.\n      /**\n       * @todo Change this to `React.useRef<Error>(undefined)` after upgrading to React 19.\n       */\n      const latestSubscriptionCallbackError = React.useRef<Error | undefined>(\n        undefined,\n      )\n\n      useIsomorphicLayoutEffect(() => {\n        isMounted.current = true\n        return () => {\n          isMounted.current = false\n        }\n      }, [])\n\n      const actualChildPropsSelector = React.useMemo(() => {\n        const selector = () => {\n          // Tricky logic here:\n          // - This render may have been triggered by a Redux store update that produced new child props\n          // - However, we may have gotten new wrapper props after that\n          // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n          // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n          // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n          if (\n            childPropsFromStoreUpdate.current &&\n            wrapperProps === lastWrapperProps.current\n          ) {\n            return childPropsFromStoreUpdate.current\n          }\n\n          // TODO We're reading the store directly in render() here. Bad idea?\n          // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n          // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n          // to determine what the child props should be.\n          return childPropsSelector(store.getState(), wrapperProps)\n        }\n        return selector\n      }, [store, wrapperProps])\n\n      // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      const subscribeForReact = React.useMemo(() => {\n        const subscribe = (reactListener: () => void) => {\n          if (!subscription) {\n            return () => {}\n          }\n\n          return subscribeUpdates(\n            shouldHandleStateChanges,\n            store,\n            subscription,\n            // @ts-ignore\n            childPropsSelector,\n            lastWrapperProps,\n            lastChildProps,\n            renderIsScheduled,\n            isMounted,\n            childPropsFromStoreUpdate,\n            notifyNestedSubs,\n            reactListener,\n          )\n        }\n\n        return subscribe\n      }, [subscription])\n\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [\n        lastWrapperProps,\n        lastChildProps,\n        renderIsScheduled,\n        wrapperProps,\n        childPropsFromStoreUpdate,\n        notifyNestedSubs,\n      ])\n\n      let actualChildProps: Record<string, unknown>\n\n      try {\n        actualChildProps = React.useSyncExternalStore(\n          // TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n          subscribeForReact,\n          // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n          // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n          actualChildPropsSelector,\n          getServerState\n            ? () => childPropsSelector(getServerState(), wrapperProps)\n            : actualChildPropsSelector,\n        )\n      } catch (err) {\n        if (latestSubscriptionCallbackError.current) {\n          // eslint-disable-next-line no-extra-semi\n          ;(err as Error).message +=\n            `\\nThe error may be correlated with this previous error:\\n${latestSubscriptionCallbackError.current.stack}\\n\\n`\n        }\n\n        throw err\n      }\n\n      useIsomorphicLayoutEffect(() => {\n        latestSubscriptionCallbackError.current = undefined\n        childPropsFromStoreUpdate.current = undefined\n        lastChildProps.current = actualChildProps\n      })\n\n      // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n      const renderedWrappedComponent = React.useMemo(() => {\n        return (\n          // @ts-ignore\n          <WrappedComponent\n            {...actualChildProps}\n            ref={reactReduxForwardedRef}\n          />\n        )\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps])\n\n      // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n      const renderedChild = React.useMemo(() => {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return (\n            <ContextToUse.Provider value={overriddenContextValue}>\n              {renderedWrappedComponent}\n            </ContextToUse.Provider>\n          )\n        }\n\n        return renderedWrappedComponent\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue])\n\n      return renderedChild\n    }\n\n    const _Connect = React.memo(ConnectFunction)\n\n    type ConnectedWrapperComponent = typeof _Connect & {\n      WrappedComponent: typeof WrappedComponent\n    }\n\n    // Add a hacky cast to get the right output type\n    const Connect = _Connect as unknown as ConnectedComponent<\n      typeof WrappedComponent,\n      WrappedComponentProps\n    >\n    Connect.WrappedComponent = WrappedComponent\n    Connect.displayName = ConnectFunction.displayName = displayName\n\n    if (forwardRef) {\n      const _forwarded = React.forwardRef(\n        function forwardConnectRef(props, ref) {\n          // @ts-ignore\n          return <Connect {...props} reactReduxForwardedRef={ref} />\n        },\n      )\n\n      const forwarded = _forwarded as ConnectedWrapperComponent\n      forwarded.displayName = displayName\n      forwarded.WrappedComponent = WrappedComponent\n      return /*#__PURE__*/ hoistStatics(forwarded, WrappedComponent)\n    }\n\n    return /*#__PURE__*/ hoistStatics(Connect, WrappedComponent)\n  }\n\n  return wrapWithConnect\n}\n\nexport default connect as Connect\n", "import type { Context, ReactNode } from 'react'\nimport { React } from '../utils/react'\nimport type { Action, Store, UnknownAction } from 'redux'\nimport type { DevModeCheckFrequency } from '../hooks/useSelector'\nimport { createSubscription } from '../utils/Subscription'\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect'\nimport type { ReactReduxContextValue } from './Context'\nimport { ReactReduxContext } from './Context'\n\nexport interface ProviderProps<\n  A extends Action<string> = UnknownAction,\n  S = unknown,\n> {\n  /**\n   * The single Redux store in your application.\n   */\n  store: Store<S, A>\n\n  /**\n   * An optional server state snapshot. Will be used during initial hydration render if available, to ensure that the UI output is consistent with the HTML generated on the server.\n   */\n  serverState?: S\n\n  /**\n   * Optional context to be used internally in react-redux. Use React.createContext() to create a context to be used.\n   * If this is used, you'll need to customize `connect` by supplying the same context provided to the Provider.\n   * Set the initial value to null, and the hooks will error\n   * if this is not overwritten by Provider.\n   */\n  context?: Context<ReactReduxContextValue<S, A> | null>\n\n  /**\n   * Determines the frequency of stability checks for all selectors.\n   * This setting overrides the global configuration for\n   * the `useSelector` stability check, allowing you to specify how often\n   * these checks should occur in development mode.\n   *\n   * @since 8.1.0\n   */\n  stabilityCheck?: DevModeCheckFrequency\n\n  /**\n   * Determines the frequency of identity function checks for all selectors.\n   * This setting overrides the global configuration for\n   * the `useSelector` identity function check, allowing you to specify how often\n   * these checks should occur in development mode.\n   *\n   * **Note**: Previously referred to as `noopCheck`.\n   *\n   * @since 9.0.0\n   */\n  identityFunctionCheck?: DevModeCheckFrequency\n\n  children: ReactNode\n}\n\nfunction Provider<A extends Action<string> = UnknownAction, S = unknown>(\n  providerProps: ProviderProps<A, S>,\n) {\n  const { children, context, serverState, store } = providerProps\n\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store)\n\n    const baseContextValue = {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined,\n    }\n\n    if (process.env.NODE_ENV === 'production') {\n      return baseContextValue\n    } else {\n      const { identityFunctionCheck = 'once', stabilityCheck = 'once' } =\n        providerProps\n\n      return /* @__PURE__ */ Object.assign(baseContextValue, {\n        stabilityCheck,\n        identityFunctionCheck,\n      })\n    }\n  }, [store, serverState])\n\n  const previousState = React.useMemo(() => store.getState(), [store])\n\n  useIsomorphicLayoutEffect(() => {\n    const { subscription } = contextValue\n    subscription.onStateChange = subscription.notifyNestedSubs\n    subscription.trySubscribe()\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs()\n    }\n    return () => {\n      subscription.tryUnsubscribe()\n      subscription.onStateChange = undefined\n    }\n  }, [contextValue, previousState])\n\n  const Context = context || ReactReduxContext\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>\n}\n\nexport default Provider\n", "import { React } from '../utils/react'\nimport { ReactReduxContext } from '../components/Context'\nimport type { ReactReduxContextValue } from '../components/Context'\n\n/**\n * Hook factory, which creates a `useReduxContext` hook bound to a given context. This is a low-level\n * hook that you should usually not need to call directly.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useReduxContext` hook bound to the specified context.\n */\nexport function createReduxContextHook(context = ReactReduxContext) {\n  return function useReduxContext(): ReactReduxContextValue {\n    const contextValue = React.useContext(context)\n\n    if (process.env.NODE_ENV !== 'production' && !contextValue) {\n      throw new Error(\n        'could not find react-redux context value; please ensure the component is wrapped in a <Provider>',\n      )\n    }\n\n    return contextValue!\n  }\n}\n\n/**\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\n * hook that you should usually not need to call directly.\n *\n * @returns {any} the value of the `ReactReduxContext`\n *\n * @example\n *\n * import React from 'react'\n * import { useReduxContext } from 'react-redux'\n *\n * export const CounterComponent = () => {\n *   const { store } = useReduxContext()\n *   return <div>{store.getState()}</div>\n * }\n */\nexport const useReduxContext = /*#__PURE__*/ createReduxContextHook()\n", "import type { Context } from 'react'\nimport type { Action, Store } from 'redux'\nimport type { ReactReduxContextValue } from '../components/Context'\nimport { ReactReduxContext } from '../components/Context'\nimport {\n  createReduxContextHook,\n  useReduxContext as useDefaultReduxContext,\n} from './useReduxContext'\n\n/**\n * Represents a type that extracts the action type from a given Redux store.\n *\n * @template StoreType - The specific type of the Redux store.\n *\n * @since 9.1.0\n * @internal\n */\nexport type ExtractStoreActionType<StoreType extends Store> =\n  StoreType extends Store<any, infer ActionType> ? ActionType : never\n\n/**\n * Represents a custom hook that provides access to the Redux store.\n *\n * @template StoreType - The specific type of the Redux store that gets returned.\n *\n * @since 9.1.0\n * @public\n */\nexport interface UseStore<StoreType extends Store> {\n  /**\n   * Returns the Redux store instance.\n   *\n   * @returns The Redux store instance.\n   */\n  (): StoreType\n\n  /**\n   * Returns the Redux store instance with specific state and action types.\n   *\n   * @returns The Redux store with the specified state and action types.\n   *\n   * @template StateType - The specific type of the state used in the store.\n   * @template ActionType - The specific type of the actions used in the store.\n   */\n  <\n    StateType extends ReturnType<StoreType['getState']> = ReturnType<\n      StoreType['getState']\n    >,\n    ActionType extends Action = ExtractStoreActionType<Store>,\n  >(): Store<StateType, ActionType>\n\n  /**\n   * Creates a \"pre-typed\" version of {@linkcode useStore useStore}\n   * where the type of the Redux `store` is predefined.\n   *\n   * This allows you to set the `store` type once, eliminating the need to\n   * specify it with every {@linkcode useStore useStore} call.\n   *\n   * @returns A pre-typed `useStore` with the store type already defined.\n   *\n   * @example\n   * ```ts\n   * export const useAppStore = useStore.withTypes<AppStore>()\n   * ```\n   *\n   * @template OverrideStoreType - The specific type of the Redux store that gets returned.\n   *\n   * @since 9.1.0\n   */\n  withTypes: <\n    OverrideStoreType extends StoreType,\n  >() => UseStore<OverrideStoreType>\n}\n\n/**\n * Hook factory, which creates a `useStore` hook bound to a given context.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useStore` hook bound to the specified context.\n */\nexport function createStoreHook<\n  StateType = unknown,\n  ActionType extends Action = Action,\n>(\n  // @ts-ignore\n  context?: Context<ReactReduxContextValue<\n    StateType,\n    ActionType\n  > | null> = ReactReduxContext,\n) {\n  const useReduxContext =\n    context === ReactReduxContext\n      ? useDefaultReduxContext\n      : // @ts-ignore\n        createReduxContextHook(context)\n  const useStore = () => {\n    const { store } = useReduxContext()\n    return store\n  }\n\n  Object.assign(useStore, {\n    withTypes: () => useStore,\n  })\n\n  return useStore as UseStore<Store<StateType, ActionType>>\n}\n\n/**\n * A hook to access the redux store.\n *\n * @returns {any} the redux store\n *\n * @example\n *\n * import React from 'react'\n * import { useStore } from 'react-redux'\n *\n * export const ExampleComponent = () => {\n *   const store = useStore()\n *   return <div>{store.getState()}</div>\n * }\n */\nexport const useStore = /*#__PURE__*/ createStoreHook()\n", "import type { Context } from 'react'\nimport type { Action, Dispatch, UnknownAction } from 'redux'\n\nimport type { ReactReduxContextValue } from '../components/Context'\nimport { ReactReduxContext } from '../components/Context'\nimport { createStoreHook, useStore as useDefaultStore } from './useStore'\n\n/**\n * Represents a custom hook that provides a dispatch function\n * from the Redux store.\n *\n * @template DispatchType - The specific type of the dispatch function.\n *\n * @since 9.1.0\n * @public\n */\nexport interface UseDispatch<\n  DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>,\n> {\n  /**\n   * Returns the dispatch function from the Redux store.\n   *\n   * @returns The dispatch function from the Redux store.\n   *\n   * @template AppDispatch - The specific type of the dispatch function.\n   */\n  <AppDispatch extends DispatchType = DispatchType>(): AppDispatch\n\n  /**\n   * Creates a \"pre-typed\" version of {@linkcode useDispatch useDispatch}\n   * where the type of the `dispatch` function is predefined.\n   *\n   * This allows you to set the `dispatch` type once, eliminating the need to\n   * specify it with every {@linkcode useDispatch useDispatch} call.\n   *\n   * @returns A pre-typed `useDispatch` with the dispatch type already defined.\n   *\n   * @example\n   * ```ts\n   * export const useAppDispatch = useDispatch.withTypes<AppDispatch>()\n   * ```\n   *\n   * @template OverrideDispatchType - The specific type of the dispatch function.\n   *\n   * @since 9.1.0\n   */\n  withTypes: <\n    OverrideDispatchType extends DispatchType,\n  >() => UseDispatch<OverrideDispatchType>\n}\n\n/**\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useDispatch` hook bound to the specified context.\n */\nexport function createDispatchHook<\n  StateType = unknown,\n  ActionType extends Action = UnknownAction,\n>(\n  // @ts-ignore\n  context?: Context<ReactReduxContextValue<\n    StateType,\n    ActionType\n  > | null> = ReactReduxContext,\n) {\n  const useStore =\n    context === ReactReduxContext ? useDefaultStore : createStoreHook(context)\n\n  const useDispatch = () => {\n    const store = useStore()\n    return store.dispatch\n  }\n\n  Object.assign(useDispatch, {\n    withTypes: () => useDispatch,\n  })\n\n  return useDispatch as UseDispatch<Dispatch<ActionType>>\n}\n\n/**\n * A hook to access the redux `dispatch` function.\n *\n * @returns {any|function} redux store's `dispatch` function\n *\n * @example\n *\n * import React, { useCallback } from 'react'\n * import { useDispatch } from 'react-redux'\n *\n * export const CounterComponent = ({ value }) => {\n *   const dispatch = useDispatch()\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\n *   return (\n *     <div>\n *       <span>{value}</span>\n *       <button onClick={increaseCounter}>Increase counter</button>\n *     </div>\n *   )\n * }\n */\nexport const useDispatch = /*#__PURE__*/ createDispatchHook()\n", "//import * as React from 'react'\nimport { React } from '../utils/react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/with-selector.js'\nimport type { ReactReduxContextValue } from '../components/Context'\nimport { ReactReduxContext } from '../components/Context'\nimport type { EqualityFn, NoInfer } from '../types'\nimport {\n  createReduxContextHook,\n  useReduxContext as useDefaultReduxContext,\n} from './useReduxContext'\n\n/**\n * The frequency of development mode checks.\n *\n * @since 8.1.0\n * @internal\n */\nexport type DevModeCheckFrequency = 'never' | 'once' | 'always'\n\n/**\n * Represents the configuration for development mode checks.\n *\n * @since 9.0.0\n * @internal\n */\nexport interface DevModeChecks {\n  /**\n   * Overrides the global stability check for the selector.\n   * - `once` - Run only the first time the selector is called.\n   * - `always` - Run every time the selector is called.\n   * - `never` - Never run the stability check.\n   *\n   * @default 'once'\n   *\n   * @since 8.1.0\n   */\n  stabilityCheck: DevModeCheckFrequency\n\n  /**\n   * Overrides the global identity function check for the selector.\n   * - `once` - Run only the first time the selector is called.\n   * - `always` - Run every time the selector is called.\n   * - `never` - Never run the identity function check.\n   *\n   * **Note**: Previously referred to as `noopCheck`.\n   *\n   * @default 'once'\n   *\n   * @since 9.0.0\n   */\n  identityFunctionCheck: DevModeCheckFrequency\n}\n\nexport interface UseSelectorOptions<Selected = unknown> {\n  equalityFn?: EqualityFn<Selected>\n\n  /**\n   * `useSelector` performs additional checks in development mode to help\n   * identify and warn about potential issues in selector behavior. This\n   * option allows you to customize the behavior of these checks per selector.\n   *\n   * @since 9.0.0\n   */\n  devModeChecks?: Partial<DevModeChecks>\n}\n\n/**\n * Represents a custom hook that allows you to extract data from the\n * Redux store state, using a selector function. The selector function\n * takes the current state as an argument and returns a part of the state\n * or some derived data. The hook also supports an optional equality\n * function or options object to customize its behavior.\n *\n * @template StateType - The specific type of state this hook operates on.\n *\n * @public\n */\nexport interface UseSelector<StateType = unknown> {\n  /**\n   * A function that takes a selector function as its first argument.\n   * The selector function is responsible for selecting a part of\n   * the Redux store's state or computing derived data.\n   *\n   * @param selector - A function that receives the current state and returns a part of the state or some derived data.\n   * @param equalityFnOrOptions - An optional equality function or options object for customizing the behavior of the selector.\n   * @returns The selected part of the state or derived data.\n   *\n   * @template TState - The specific type of state this hook operates on.\n   * @template Selected - The type of the value that the selector function will return.\n   */\n  <TState extends StateType = StateType, Selected = unknown>(\n    selector: (state: TState) => Selected,\n    equalityFnOrOptions?: EqualityFn<Selected> | UseSelectorOptions<Selected>,\n  ): Selected\n\n  /**\n   * Creates a \"pre-typed\" version of {@linkcode useSelector useSelector}\n   * where the `state` type is predefined.\n   *\n   * This allows you to set the `state` type once, eliminating the need to\n   * specify it with every {@linkcode useSelector useSelector} call.\n   *\n   * @returns A pre-typed `useSelector` with the state type already defined.\n   *\n   * @example\n   * ```ts\n   * export const useAppSelector = useSelector.withTypes<RootState>()\n   * ```\n   *\n   * @template OverrideStateType - The specific type of state this hook operates on.\n   *\n   * @since 9.1.0\n   */\n  withTypes: <\n    OverrideStateType extends StateType,\n  >() => UseSelector<OverrideStateType>\n}\n\nconst refEquality: EqualityFn<any> = (a, b) => a === b\n\n/**\n * Hook factory, which creates a `useSelector` hook bound to a given context.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useSelector` hook bound to the specified context.\n */\nexport function createSelectorHook(\n  context: React.Context<ReactReduxContextValue<\n    any,\n    any\n  > | null> = ReactReduxContext,\n): UseSelector {\n  const useReduxContext =\n    context === ReactReduxContext\n      ? useDefaultReduxContext\n      : createReduxContextHook(context)\n\n  const useSelector = <TState, Selected>(\n    selector: (state: TState) => Selected,\n    equalityFnOrOptions:\n      | EqualityFn<NoInfer<Selected>>\n      | UseSelectorOptions<NoInfer<Selected>> = {},\n  ): Selected => {\n    const { equalityFn = refEquality } =\n      typeof equalityFnOrOptions === 'function'\n        ? { equalityFn: equalityFnOrOptions }\n        : equalityFnOrOptions\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(`You must pass a selector to useSelector`)\n      }\n      if (typeof selector !== 'function') {\n        throw new Error(`You must pass a function as a selector to useSelector`)\n      }\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\n          `You must pass a function as an equality function to useSelector`,\n        )\n      }\n    }\n\n    const reduxContext = useReduxContext()\n\n    const { store, subscription, getServerState } = reduxContext\n\n    const firstRun = React.useRef(true)\n\n    const wrappedSelector = React.useCallback<typeof selector>(\n      {\n        [selector.name](state: TState) {\n          const selected = selector(state)\n          if (process.env.NODE_ENV !== 'production') {\n            const { devModeChecks = {} } =\n              typeof equalityFnOrOptions === 'function'\n                ? {}\n                : equalityFnOrOptions\n            const { identityFunctionCheck, stabilityCheck } = reduxContext\n            const {\n              identityFunctionCheck: finalIdentityFunctionCheck,\n              stabilityCheck: finalStabilityCheck,\n            } = {\n              stabilityCheck,\n              identityFunctionCheck,\n              ...devModeChecks,\n            }\n            if (\n              finalStabilityCheck === 'always' ||\n              (finalStabilityCheck === 'once' && firstRun.current)\n            ) {\n              const toCompare = selector(state)\n              if (!equalityFn(selected, toCompare)) {\n                let stack: string | undefined = undefined\n                try {\n                  throw new Error()\n                } catch (e) {\n                  // eslint-disable-next-line no-extra-semi\n                  ;({ stack } = e as Error)\n                }\n                console.warn(\n                  'Selector ' +\n                    (selector.name || 'unknown') +\n                    ' returned a different result when called with the same parameters. This can lead to unnecessary rerenders.' +\n                    '\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization',\n                  {\n                    state,\n                    selected,\n                    selected2: toCompare,\n                    stack,\n                  },\n                )\n              }\n            }\n            if (\n              finalIdentityFunctionCheck === 'always' ||\n              (finalIdentityFunctionCheck === 'once' && firstRun.current)\n            ) {\n              // @ts-ignore\n              if (selected === state) {\n                let stack: string | undefined = undefined\n                try {\n                  throw new Error()\n                } catch (e) {\n                  // eslint-disable-next-line no-extra-semi\n                  ;({ stack } = e as Error)\n                }\n                console.warn(\n                  'Selector ' +\n                    (selector.name || 'unknown') +\n                    ' returned the root state when called. This can lead to unnecessary rerenders.' +\n                    '\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.',\n                  { stack },\n                )\n              }\n            }\n            if (firstRun.current) firstRun.current = false\n          }\n          return selected\n        },\n      }[selector.name],\n      [selector],\n    )\n\n    const selectedState = useSyncExternalStoreWithSelector(\n      subscription.addNestedSub,\n      store.getState,\n      getServerState || store.getState,\n      wrappedSelector,\n      equalityFn,\n    )\n\n    React.useDebugValue(selectedState)\n\n    return selectedState\n  }\n\n  Object.assign(useSelector, {\n    withTypes: () => useSelector,\n  })\n\n  return useSelector as UseSelector\n}\n\n/**\n * A hook to access the redux store's state. This hook takes a selector function\n * as an argument. The selector is called with the store state.\n *\n * This hook takes an optional equality comparison function as the second parameter\n * that allows you to customize the way the selected state is compared to determine\n * whether the component needs to be re-rendered.\n *\n * @param {Function} selector the selector function\n * @param {Function=} equalityFn the function that will be used to determine equality\n *\n * @returns {any} the selected state\n *\n * @example\n *\n * import React from 'react'\n * import { useSelector } from 'react-redux'\n *\n * export const CounterComponent = () => {\n *   const counter = useSelector(state => state.counter)\n *   return <div>{counter}</div>\n * }\n */\nexport const useSelector = /*#__PURE__*/ createSelectorHook()\n", "import connect from './components/connect'\nexport type {\n  Connect,\n  ConnectProps,\n  ConnectedProps,\n} from './components/connect'\n\nimport shallowEqual from './utils/shallowEqual'\n\nimport Provider from './components/Provider'\nimport { defaultNoopBatch } from './utils/batch'\n\nexport { ReactReduxContext } from './components/Context'\nexport type { ReactReduxContextValue } from './components/Context'\n\nexport type { ProviderProps } from './components/Provider'\n\nexport type {\n  MapDispatchToProps,\n  MapDispatchToPropsFactory,\n  MapDispatchToPropsFunction,\n  MapDispatchToPropsNonObject,\n  MapDispatchToPropsParam,\n  MapStateToProps,\n  MapStateToPropsFactory,\n  MapStateToPropsParam,\n  MergeProps,\n  Selector,\n  SelectorFactory,\n} from './connect/selectorFactory'\n\nexport { createDispatchHook, useDispatch } from './hooks/useDispatch'\nexport type { UseDispatch } from './hooks/useDispatch'\n\nexport { createSelectorHook, useSelector } from './hooks/useSelector'\nexport type { UseSelector } from './hooks/useSelector'\n\nexport { createStoreHook, useStore } from './hooks/useStore'\nexport type { UseStore } from './hooks/useStore'\n\nexport type { Subscription } from './utils/Subscription'\n\nexport * from './types'\n\n/**\n * @deprecated As of React 18, batching is enabled by default for ReactDOM and React Native.\n * This is now a no-op that immediately runs the callback.\n */\nconst batch = defaultNoopBatch\n\nexport { Provider, batch, connect, shallowEqual }\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;;;ACQhB,IAAMC,WAAA,GAA8B,eAAAD,KAAA,CAAME,OAAA,CAAQC,UAAA,CAAW,IAAI;AAExE,IAAMC,kBAAA,GAAqC,eAAAC,MAAA,CAAOC,GAAA,CAChDL,WAAA,GAAc,+BAA+B,eAC/C;AACA,IAAMM,iBAAA,GAAoC,eAAAF,MAAA,CAAOC,GAAA,CAAI,cAAc;AACnE,IAAME,mBAAA,GAAsC,eAAAH,MAAA,CAAOC,GAAA,CAAI,gBAAgB;AACvE,IAAMG,sBAAA,GAAyC,eAAAJ,MAAA,CAAOC,GAAA,CAAI,mBAAmB;AAC7E,IAAMI,mBAAA,GAAsC,eAAAL,MAAA,CAAOC,GAAA,CAAI,gBAAgB;AACvE,IAAMK,mBAAA,GAAsC,eAAAN,MAAA,CAAOC,GAAA,CAAI,gBAAgB;AACvE,IAAMM,kBAAA,GAAqC,eAAAP,MAAA,CAAOC,GAAA,CAAI,eAAe;AACrE,IAAMO,sBAAA,GAAyC,eAAAR,MAAA,CAAOC,GAAA,CAAI,mBAAmB;AAC7E,IAAMQ,mBAAA,GAAsC,eAAAT,MAAA,CAAOC,GAAA,CAAI,gBAAgB;AACvE,IAAMS,wBAAA,GAA2C,eAAAV,MAAA,CAAOC,GAAA,CACtD,qBACF;AACA,IAAMU,eAAA,GAAkC,eAAAX,MAAA,CAAOC,GAAA,CAAI,YAAY;AAC/D,IAAMW,eAAA,GAAkC,eAAAZ,MAAA,CAAOC,GAAA,CAAI,YAAY;AAC/D,IAAMY,oBAAA,GAAuC,eAAAb,MAAA,CAAOC,GAAA,CAAI,iBAAiB;AACzE,IAAMa,sBAAA,GAAyC,eAAAd,MAAA,CAAOC,GAAA,CACpD,wBACF;AAEO,IAAMc,UAAA,GAAaP,sBAAA;AACnB,IAAMQ,IAAA,GAAOL,eAAA;AAEb,SAASM,mBAAmBC,IAAA,EAAgC;EACjE,OAAO,OAAOA,IAAA,KAAS,YACrB,OAAOA,IAAA,KAAS,cAChBA,IAAA,KAASf,mBAAA,IACTe,IAAA,KAASb,mBAAA,IACTa,IAAA,KAASd,sBAAA,IACTc,IAAA,KAAST,mBAAA,IACTS,IAAA,KAASR,wBAAA,IACTQ,IAAA,KAASL,oBAAA,IACR,OAAOK,IAAA,KAAS,YACfA,IAAA,KAAS,SACRA,IAAA,CAAKC,QAAA,KAAaP,eAAA,IACjBM,IAAA,CAAKC,QAAA,KAAaR,eAAA,IAClBO,IAAA,CAAKC,QAAA,KAAaZ,kBAAA,IAClBW,IAAA,CAAKC,QAAA,KAAab,mBAAA,IAClBY,IAAA,CAAKC,QAAA,KAAaX,sBAAA,IAClBU,IAAA,CAAKC,QAAA,KAAaL,sBAAA,IAClBI,IAAA,CAAKE,WAAA,KAAgB,UACvB,OACA;AACN;AAEA,SAASC,OAAOC,MAAA,EAAiC;EAC/C,IAAI,OAAOA,MAAA,KAAW,YAAYA,MAAA,KAAW,MAAM;IACjD,MAAM;MAAEH;IAAS,IAAIG,MAAA;IAErB,QAAQH,QAAA;MACN,KAAKpB,kBAAA;QACH,QAAUuB,MAAA,GAASA,MAAA,CAAOJ,IAAA,EAAOI,MAAA;UAC/B,KAAKnB,mBAAA;UACL,KAAKE,mBAAA;UACL,KAAKD,sBAAA;UACL,KAAKK,mBAAA;UACL,KAAKC,wBAAA;YACH,OAAOY,MAAA;UACT;YACE,QAAUA,MAAA,GAASA,MAAA,IAAUA,MAAA,CAAOH,QAAA,EAAWG,MAAA;cAC7C,KAAKf,kBAAA;cACL,KAAKC,sBAAA;cACL,KAAKI,eAAA;cACL,KAAKD,eAAA;gBACH,OAAOW,MAAA;cACT,KAAKhB,mBAAA;gBACH,OAAOgB,MAAA;cACT;gBACE,OAAOH,QAAA;YACX;QACJ;MACF,KAAKjB,iBAAA;QACH,OAAOiB,QAAA;IACX;EACF;AACF;AAEO,SAASI,kBAAkBD,MAAA,EAAqC;EACrE,OAAO1B,WAAA,GACHyB,MAAA,CAAOC,MAAM,MAAMhB,mBAAA,GACnBe,MAAA,CAAOC,MAAM,MAAMf,kBAAA;AACzB;AAEO,SAASiB,OAAOF,MAAA,EAAiD;EACtE,OAAOD,MAAA,CAAOC,MAAM,MAAMX,eAAA;AAC5B;;;AC1Fe,SAARc,QAAyBC,OAAA,EAAiB;EAE/C,IAAI,OAAOC,OAAA,KAAY,eAAe,OAAOA,OAAA,CAAQC,KAAA,KAAU,YAAY;IACzED,OAAA,CAAQC,KAAA,CAAMF,OAAO;EACvB;EAEA,IAAI;IAIF,MAAM,IAAIG,KAAA,CAAMH,OAAO;EAEzB,SAASI,CAAA,EAAG,CAAC;AAEf;;;AClBA,SAASC,OAAOC,QAAA,EAAmBC,UAAA,EAA0B;EAC3D,IAAI,CAACD,QAAA,EAAU;IACb,MAAM,IAAIH,KAAA,CAAM,wBAAwBI,UAAU,cAAc;EAClE,WACEA,UAAA,KAAe,qBACfA,UAAA,KAAe,sBACf;IACA,IAAI,CAACC,MAAA,CAAOC,SAAA,CAAUC,cAAA,CAAeC,IAAA,CAAKL,QAAA,EAAU,mBAAmB,GAAG;MACxEP,OAAA,CACE,oBAAoBQ,UAAU,4DAChC;IACF;EACF;AACF;AAEe,SAARK,mBACLC,eAAA,EACAC,kBAAA,EACAC,UAAA,EACM;EACNV,MAAA,CAAOQ,eAAA,EAAiB,iBAAiB;EACzCR,MAAA,CAAOS,kBAAA,EAAoB,oBAAoB;EAC/CT,MAAA,CAAOU,UAAA,EAAY,YAAY;AACjC;;;ACyCA,SAASC,8BAOPH,eAAA,EACAC,kBAAA,EACAC,UAAA,EACAE,QAAA,EACA;EACEC,cAAA;EACAC,gBAAA;EACAC;AACF,GACA;EACA,IAAIC,iBAAA,GAAoB;EACxB,IAAIC,KAAA;EACJ,IAAIC,QAAA;EACJ,IAAIC,UAAA;EACJ,IAAIC,aAAA;EACJ,IAAIC,WAAA;EAEJ,SAASC,gBAAgBC,UAAA,EAAmBC,aAAA,EAA0B;IACpEP,KAAA,GAAQM,UAAA;IACRL,QAAA,GAAWM,aAAA;IACXL,UAAA,GAAaX,eAAA,CAAgBS,KAAA,EAAOC,QAAQ;IAC5CE,aAAA,GAAgBX,kBAAA,CAAmBG,QAAA,EAAUM,QAAQ;IACrDG,WAAA,GAAcX,UAAA,CAAWS,UAAA,EAAYC,aAAA,EAAeF,QAAQ;IAC5DF,iBAAA,GAAoB;IACpB,OAAOK,WAAA;EACT;EAEA,SAASI,0BAAA,EAA4B;IACnCN,UAAA,GAAaX,eAAA,CAAgBS,KAAA,EAAOC,QAAQ;IAE5C,IAAIT,kBAAA,CAAmBiB,iBAAA,EACrBN,aAAA,GAAgBX,kBAAA,CAAmBG,QAAA,EAAUM,QAAQ;IAEvDG,WAAA,GAAcX,UAAA,CAAWS,UAAA,EAAYC,aAAA,EAAeF,QAAQ;IAC5D,OAAOG,WAAA;EACT;EAEA,SAASM,eAAA,EAAiB;IACxB,IAAInB,eAAA,CAAgBkB,iBAAA,EAClBP,UAAA,GAAaX,eAAA,CAAgBS,KAAA,EAAOC,QAAQ;IAE9C,IAAIT,kBAAA,CAAmBiB,iBAAA,EACrBN,aAAA,GAAgBX,kBAAA,CAAmBG,QAAA,EAAUM,QAAQ;IAEvDG,WAAA,GAAcX,UAAA,CAAWS,UAAA,EAAYC,aAAA,EAAeF,QAAQ;IAC5D,OAAOG,WAAA;EACT;EAEA,SAASO,eAAA,EAAiB;IACxB,MAAMC,cAAA,GAAiBrB,eAAA,CAAgBS,KAAA,EAAOC,QAAQ;IACtD,MAAMY,iBAAA,GAAoB,CAACf,kBAAA,CAAmBc,cAAA,EAAgBV,UAAU;IACxEA,UAAA,GAAaU,cAAA;IAEb,IAAIC,iBAAA,EACFT,WAAA,GAAcX,UAAA,CAAWS,UAAA,EAAYC,aAAA,EAAeF,QAAQ;IAE9D,OAAOG,WAAA;EACT;EAEA,SAASU,sBAAsBC,SAAA,EAAkBC,YAAA,EAAyB;IACxE,MAAMC,YAAA,GAAe,CAACpB,gBAAA,CAAiBmB,YAAA,EAAcf,QAAQ;IAC7D,MAAMiB,YAAA,GAAe,CAACtB,cAAA,CACpBmB,SAAA,EACAf,KAAA,EACAgB,YAAA,EACAf,QACF;IACAD,KAAA,GAAQe,SAAA;IACRd,QAAA,GAAWe,YAAA;IAEX,IAAIC,YAAA,IAAgBC,YAAA,EAAc,OAAOV,yBAAA,CAA0B;IACnE,IAAIS,YAAA,EAAc,OAAOP,cAAA,CAAe;IACxC,IAAIQ,YAAA,EAAc,OAAOP,cAAA,CAAe;IACxC,OAAOP,WAAA;EACT;EAEA,OAAO,SAASe,uBACdJ,SAAA,EACAC,YAAA,EACA;IACA,OAAOjB,iBAAA,GACHe,qBAAA,CAAsBC,SAAA,EAAWC,YAAY,IAC7CX,eAAA,CAAgBU,SAAA,EAAWC,YAAY;EAC7C;AACF;AAgDe,SAARI,0BAOLzB,QAAA,EACA;EACE0B,mBAAA;EACAC,sBAAA;EACAC,cAAA;EACA,GAAGC;AACL,GAOA;EACA,MAAMjC,eAAA,GAAkB8B,mBAAA,CAAoB1B,QAAA,EAAU6B,OAAO;EAC7D,MAAMhC,kBAAA,GAAqB8B,sBAAA,CAAuB3B,QAAA,EAAU6B,OAAO;EACnE,MAAM/B,UAAA,GAAa8B,cAAA,CAAe5B,QAAA,EAAU6B,OAAO;EAEnD,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzCrC,kBAAA,CAAmBC,eAAA,EAAiBC,kBAAA,EAAoBC,UAAU;EACpE;EAEA,OAAOC,6BAAA,CAMLH,eAAA,EAAiBC,kBAAA,EAAoBC,UAAA,EAAYE,QAAA,EAAU6B,OAAO;AACtE;;;AC/Oe,SAARI,mBACLC,cAAA,EACAlC,QAAA,EACyB;EACzB,MAAMmC,mBAAA,GAA+C,CAAC;EAEtD,WAAWC,GAAA,IAAOF,cAAA,EAAgB;IAChC,MAAMG,aAAA,GAAgBH,cAAA,CAAeE,GAAG;IACxC,IAAI,OAAOC,aAAA,KAAkB,YAAY;MACvCF,mBAAA,CAAoBC,GAAG,IAAI,IAAIE,IAAA,KAAStC,QAAA,CAASqC,aAAA,CAAc,GAAGC,IAAI,CAAC;IACzE;EACF;EACA,OAAOH,mBAAA;AACT;;;ACXe,SAARI,cAA+BC,GAAA,EAAc;EAClD,IAAI,OAAOA,GAAA,KAAQ,YAAYA,GAAA,KAAQ,MAAM,OAAO;EAEpD,MAAMC,KAAA,GAAQlD,MAAA,CAAOmD,cAAA,CAAeF,GAAG;EACvC,IAAIC,KAAA,KAAU,MAAM,OAAO;EAE3B,IAAIE,SAAA,GAAYF,KAAA;EAChB,OAAOlD,MAAA,CAAOmD,cAAA,CAAeC,SAAS,MAAM,MAAM;IAChDA,SAAA,GAAYpD,MAAA,CAAOmD,cAAA,CAAeC,SAAS;EAC7C;EAEA,OAAOF,KAAA,KAAUE,SAAA;AACnB;;;ACbe,SAARC,kBACLC,KAAA,EACAC,WAAA,EACAxD,UAAA,EACA;EACA,IAAI,CAACiD,aAAA,CAAcM,KAAK,GAAG;IACzB/D,OAAA,CACE,GAAGQ,UAAU,SAASwD,WAAW,iDAAiDD,KAAK,GACzF;EACF;AACF;;;ACGO,SAASE,uBAMdC,WAAA,EAOA;EACA,OAAO,SAASC,qBAAqBjD,QAAA,EAAoB;IACvD,MAAMkD,QAAA,GAAWF,WAAA,CAAYhD,QAAQ;IAErC,SAASmD,iBAAA,EAAmB;MAC1B,OAAOD,QAAA;IACT;IACAC,gBAAA,CAAiBrC,iBAAA,GAAoB;IACrC,OAAOqC,gBAAA;EACT;AACF;AAUA,SAASC,qBAAqBC,UAAA,EAAwB;EACpD,OAAOA,UAAA,CAAWvC,iBAAA,GACdwC,OAAA,CAAQD,UAAA,CAAWvC,iBAAiB,IACpCuC,UAAA,CAAWE,MAAA,KAAW;AAC5B;AAcO,SAASC,mBACdH,UAAA,EACA/D,UAAA,EACA;EACA,OAAO,SAASmE,kBACdzD,QAAA,EACA;IAAE8C;EAAY,GACd;IACA,MAAMY,KAAA,GAAQ,SAASC,gBACrBC,eAAA,EACAtD,QAAA,EACY;MACZ,OAAOoD,KAAA,CAAM5C,iBAAA,GACT4C,KAAA,CAAML,UAAA,CAAWO,eAAA,EAAiBtD,QAAQ,IAC1CoD,KAAA,CAAML,UAAA,CAAWO,eAAA,EAAiB,MAAS;IACjD;IAGAF,KAAA,CAAM5C,iBAAA,GAAoB;IAE1B4C,KAAA,CAAML,UAAA,GAAa,SAASQ,uBAC1BD,eAAA,EACAtD,QAAA,EACY;MACZoD,KAAA,CAAML,UAAA,GAAaA,UAAA;MACnBK,KAAA,CAAM5C,iBAAA,GAAoBsC,oBAAA,CAAqBC,UAAU;MACzD,IAAIS,KAAA,GAAQJ,KAAA,CAAME,eAAA,EAAiBtD,QAAQ;MAE3C,IAAI,OAAOwD,KAAA,KAAU,YAAY;QAC/BJ,KAAA,CAAML,UAAA,GAAaS,KAAA;QACnBJ,KAAA,CAAM5C,iBAAA,GAAoBsC,oBAAA,CAAqBU,KAAK;QACpDA,KAAA,GAAQJ,KAAA,CAAME,eAAA,EAAiBtD,QAAQ;MACzC;MAEA,IAAIwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAC3BY,iBAAA,CAAkBkB,KAAA,EAAOhB,WAAA,EAAaxD,UAAU;MAElD,OAAOwE,KAAA;IACT;IAEA,OAAOJ,KAAA;EACT;AACF;;;AC3GO,SAASK,wBAAwBC,GAAA,EAAcC,IAAA,EAAc;EAClE,OAAO,CACLjE,QAAA,EACA6B,OAAA,KACG;IACH,MAAM,IAAI3C,KAAA,CACR,yBAAyB,OAAO8E,GAAG,QAAQC,IAAI,uCAC7CpC,OAAA,CAAQqC,oBACV,GACF;EACF;AACF;;;ACPO,SAASC,0BACdtE,kBAAA,EAGA;EACA,OAAOA,kBAAA,IAAsB,OAAOA,kBAAA,KAAuB,WACvDkD,sBAAA,CAAwB/C,QAAA;EAAA;EAEtBiC,kBAAA,CAAmBpC,kBAAA,EAAoBG,QAAQ,CACjD,IACA,CAACH,kBAAA,GACCkD,sBAAA,CAAwB/C,QAAA,KAAwC;IAC9DA;EACF,EAAE,IACF,OAAOH,kBAAA,KAAuB;EAAA;EAE5B2D,kBAAA,CAAmB3D,kBAAA,EAAoB,oBAAoB,IAC3DkE,uBAAA,CAAwBlE,kBAAA,EAAoB,oBAAoB;AAC1E;;;ACpBO,SAASuE,uBACdxE,eAAA,EACA;EACA,OAAO,CAACA,eAAA,GACJmD,sBAAA,CAAuB,OAAO,CAAC,EAAE,IACjC,OAAOnD,eAAA,KAAoB;EAAA;EAEzB4D,kBAAA,CAAmB5D,eAAA,EAAiB,iBAAiB,IACrDmE,uBAAA,CAAwBnE,eAAA,EAAiB,iBAAiB;AAClE;;;ACPA,SAASyE,kBAMP9D,UAAA,EACAC,aAAA,EACAF,QAAA,EACc;EAEd,OAAO;IAAE,GAAGA,QAAA;IAAU,GAAGC,UAAA;IAAY,GAAGC;EAAc;AACxD;AAEA,SAAS8D,mBAMPxE,UAAA,EAOoE;EACpE,OAAO,SAASyE,oBACdvE,QAAA,EACA;IAAE8C,WAAA;IAAa0B;EAAoB,GACnC;IACA,IAAIC,UAAA,GAAa;IACjB,IAAIhE,WAAA;IAEJ,OAAO,SAASiE,gBACdnE,UAAA,EACAC,aAAA,EACAF,QAAA,EACA;MACA,MAAMqE,eAAA,GAAkB7E,UAAA,CAAWS,UAAA,EAAYC,aAAA,EAAeF,QAAQ;MAEtE,IAAImE,UAAA,EAAY;QACd,IAAI,CAACD,mBAAA,CAAoBG,eAAA,EAAiBlE,WAAW,GACnDA,WAAA,GAAckE,eAAA;MAClB,OAAO;QACLF,UAAA,GAAa;QACbhE,WAAA,GAAckE,eAAA;QAEd,IAAI7C,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAC3BY,iBAAA,CAAkBnC,WAAA,EAAaqC,WAAA,EAAa,YAAY;MAC5D;MAEA,OAAOrC,WAAA;IACT;EACF;AACF;AAEO,SAASmE,kBAMd9E,UAAA,EACA;EACA,OAAO,CAACA,UAAA,GACJ,MAAMuE,iBAAA,GACN,OAAOvE,UAAA,KAAe,aACpBwE,kBAAA,CAAmBxE,UAAU,IAC7BiE,uBAAA,CAAwBjE,UAAA,EAAY,YAAY;AACxD;;;AC5EO,SAAS+E,iBAAiBC,QAAA,EAAsB;EACrDA,QAAA,CAAS;AACX;;;ACWA,SAASC,yBAAA,EAA2B;EAClC,IAAIC,KAAA,GAAyB;EAC7B,IAAIC,IAAA,GAAwB;EAE5B,OAAO;IACLC,MAAA,EAAQ;MACNF,KAAA,GAAQ;MACRC,IAAA,GAAO;IACT;IAEAE,OAAA,EAAS;MACPN,gBAAA,CAAM,MAAM;QACV,IAAIO,QAAA,GAAWJ,KAAA;QACf,OAAOI,QAAA,EAAU;UACfA,QAAA,CAASN,QAAA,CAAS;UAClBM,QAAA,GAAWA,QAAA,CAASC,IAAA;QACtB;MACF,CAAC;IACH;IAEAC,IAAA,EAAM;MACJ,MAAMC,SAAA,GAAwB,EAAC;MAC/B,IAAIH,QAAA,GAAWJ,KAAA;MACf,OAAOI,QAAA,EAAU;QACfG,SAAA,CAAUC,IAAA,CAAKJ,QAAQ;QACvBA,QAAA,GAAWA,QAAA,CAASC,IAAA;MACtB;MACA,OAAOE,SAAA;IACT;IAEAE,UAAUX,QAAA,EAAsB;MAC9B,IAAIY,YAAA,GAAe;MAEnB,MAAMN,QAAA,GAAsBH,IAAA,GAAO;QACjCH,QAAA;QACAO,IAAA,EAAM;QACNM,IAAA,EAAMV;MACR;MAEA,IAAIG,QAAA,CAASO,IAAA,EAAM;QACjBP,QAAA,CAASO,IAAA,CAAKN,IAAA,GAAOD,QAAA;MACvB,OAAO;QACLJ,KAAA,GAAQI,QAAA;MACV;MAEA,OAAO,SAASQ,YAAA,EAAc;QAC5B,IAAI,CAACF,YAAA,IAAgBV,KAAA,KAAU,MAAM;QACrCU,YAAA,GAAe;QAEf,IAAIN,QAAA,CAASC,IAAA,EAAM;UACjBD,QAAA,CAASC,IAAA,CAAKM,IAAA,GAAOP,QAAA,CAASO,IAAA;QAChC,OAAO;UACLV,IAAA,GAAOG,QAAA,CAASO,IAAA;QAClB;QACA,IAAIP,QAAA,CAASO,IAAA,EAAM;UACjBP,QAAA,CAASO,IAAA,CAAKN,IAAA,GAAOD,QAAA,CAASC,IAAA;QAChC,OAAO;UACLL,KAAA,GAAQI,QAAA,CAASC,IAAA;QACnB;MACF;IACF;EACF;AACF;AAeA,IAAMQ,aAAA,GAAgB;EACpBV,OAAA,EAAS,CAAC;EACVG,GAAA,EAAKA,CAAA,KAAM;AACb;AAEO,SAASQ,mBAAmBC,KAAA,EAAYC,SAAA,EAA0B;EACvE,IAAIJ,WAAA;EACJ,IAAIL,SAAA,GAAgCM,aAAA;EAGpC,IAAII,mBAAA,GAAsB;EAG1B,IAAIC,cAAA,GAAiB;EAErB,SAASC,aAAaf,QAAA,EAAsB;IAC1CgB,YAAA,CAAa;IAEb,MAAMC,eAAA,GAAkBd,SAAA,CAAUE,SAAA,CAAUL,QAAQ;IAGpD,IAAIkB,OAAA,GAAU;IACd,OAAO,MAAM;MACX,IAAI,CAACA,OAAA,EAAS;QACZA,OAAA,GAAU;QACVD,eAAA,CAAgB;QAChBE,cAAA,CAAe;MACjB;IACF;EACF;EAEA,SAASC,iBAAA,EAAmB;IAC1BjB,SAAA,CAAUJ,MAAA,CAAO;EACnB;EAEA,SAASsB,oBAAA,EAAsB;IAC7B,IAAIC,YAAA,CAAaC,aAAA,EAAe;MAC9BD,YAAA,CAAaC,aAAA,CAAc;IAC7B;EACF;EAEA,SAASjB,aAAA,EAAe;IACtB,OAAOQ,cAAA;EACT;EAEA,SAASE,aAAA,EAAe;IACtBH,mBAAA;IACA,IAAI,CAACL,WAAA,EAAa;MAChBA,WAAA,GAAcI,SAAA,GACVA,SAAA,CAAUG,YAAA,CAAaM,mBAAmB,IAC1CV,KAAA,CAAMN,SAAA,CAAUgB,mBAAmB;MAEvClB,SAAA,GAAYR,wBAAA,CAAyB;IACvC;EACF;EAEA,SAASwB,eAAA,EAAiB;IACxBN,mBAAA;IACA,IAAIL,WAAA,IAAeK,mBAAA,KAAwB,GAAG;MAC5CL,WAAA,CAAY;MACZA,WAAA,GAAc;MACdL,SAAA,CAAUL,KAAA,CAAM;MAChBK,SAAA,GAAYM,aAAA;IACd;EACF;EAEA,SAASe,iBAAA,EAAmB;IAC1B,IAAI,CAACV,cAAA,EAAgB;MACnBA,cAAA,GAAiB;MACjBE,YAAA,CAAa;IACf;EACF;EAEA,SAASS,mBAAA,EAAqB;IAC5B,IAAIX,cAAA,EAAgB;MAClBA,cAAA,GAAiB;MACjBK,cAAA,CAAe;IACjB;EACF;EAEA,MAAMG,YAAA,GAA6B;IACjCP,YAAA;IACAK,gBAAA;IACAC,mBAAA;IACAf,YAAA;IACAU,YAAA,EAAcQ,gBAAA;IACdL,cAAA,EAAgBM,kBAAA;IAChBC,YAAA,EAAcA,CAAA,KAAMvB;EACtB;EAEA,OAAOmB,YAAA;AACT;;;AC1KA,IAAMK,SAAA,GAAYA,CAAA,KAChB,CAAC,EACC,OAAOC,MAAA,KAAW,eAClB,OAAOA,MAAA,CAAOC,QAAA,KAAa,eAC3B,OAAOD,MAAA,CAAOC,QAAA,CAASC,aAAA,KAAkB;AAG7C,IAAMC,KAAA,GAAwB,eAAAJ,SAAA,CAAU;AAWxC,IAAMK,sBAAA,GAAyBA,CAAA,KAC7B,OAAOC,SAAA,KAAc,eAAeA,SAAA,CAAUC,OAAA,KAAY;AAE5D,IAAMC,aAAA,GAAgC,eAAAH,sBAAA,CAAuB;AAE7D,IAAMI,4BAAA,GAA+BA,CAAA,KACnCL,KAAA,IAASI,aAAA,GAAgBvK,KAAA,CAAMyK,eAAA,GAAkBzK,KAAA,CAAM0K,SAAA;AAElD,IAAMC,yBAAA,GACK,eAAAH,4BAAA,CAA6B;;;ACvC/C,SAASI,GAAGC,CAAA,EAAYC,CAAA,EAAY;EAClC,IAAID,CAAA,KAAMC,CAAA,EAAG;IACX,OAAOD,CAAA,KAAM,KAAKC,CAAA,KAAM,KAAK,IAAID,CAAA,KAAM,IAAIC,CAAA;EAC7C,OAAO;IACL,OAAOD,CAAA,KAAMA,CAAA,IAAKC,CAAA,KAAMA,CAAA;EAC1B;AACF;AAEe,SAARC,aAA8BC,IAAA,EAAWC,IAAA,EAAW;EACzD,IAAIL,EAAA,CAAGI,IAAA,EAAMC,IAAI,GAAG,OAAO;EAE3B,IACE,OAAOD,IAAA,KAAS,YAChBA,IAAA,KAAS,QACT,OAAOC,IAAA,KAAS,YAChBA,IAAA,KAAS,MACT;IACA,OAAO;EACT;EAEA,MAAMC,KAAA,GAAQ3I,MAAA,CAAO4I,IAAA,CAAKH,IAAI;EAC9B,MAAMI,KAAA,GAAQ7I,MAAA,CAAO4I,IAAA,CAAKF,IAAI;EAE9B,IAAIC,KAAA,CAAM3E,MAAA,KAAW6E,KAAA,CAAM7E,MAAA,EAAQ,OAAO;EAE1C,SAAS8E,CAAA,GAAI,GAAGA,CAAA,GAAIH,KAAA,CAAM3E,MAAA,EAAQ8E,CAAA,IAAK;IACrC,IACE,CAAC9I,MAAA,CAAOC,SAAA,CAAUC,cAAA,CAAeC,IAAA,CAAKuI,IAAA,EAAMC,KAAA,CAAMG,CAAC,CAAC,KACpD,CAACT,EAAA,CAAGI,IAAA,CAAKE,KAAA,CAAMG,CAAC,CAAC,GAAGJ,IAAA,CAAKC,KAAA,CAAMG,CAAC,CAAC,CAAC,GAClC;MACA,OAAO;IACT;EACF;EAEA,OAAO;AACT;;;ACxBA,IAAMC,aAAA,GAAgB;EACpBC,iBAAA,EAAmB;EACnBC,WAAA,EAAa;EACbC,YAAA,EAAc;EACdC,YAAA,EAAc;EACd5F,WAAA,EAAa;EACb6F,eAAA,EAAiB;EACjBC,wBAAA,EAA0B;EAC1BC,wBAAA,EAA0B;EAC1BC,MAAA,EAAQ;EACRC,SAAA,EAAW;EACXxK,IAAA,EAAM;AACR;AAEA,IAAMyK,aAAA,GAAgB;EACpB/E,IAAA,EAAM;EACNV,MAAA,EAAQ;EACR/D,SAAA,EAAW;EACXyJ,MAAA,EAAQ;EACRC,MAAA,EAAQ;EACRC,SAAA,EAAW;EACXC,KAAA,EAAO;AACT;AAEA,IAAMC,mBAAA,GAAsB;EAC1B7K,QAAA,EAAU;EACV8K,MAAA,EAAQ;EACRZ,YAAA,EAAc;EACd5F,WAAA,EAAa;EACbiG,SAAA,EAAW;AACb;AAEA,IAAMQ,YAAA,GAAe;EACnB/K,QAAA,EAAU;EACVgL,OAAA,EAAS;EACTd,YAAA,EAAc;EACd5F,WAAA,EAAa;EACbiG,SAAA,EAAW;EACXxK,IAAA,EAAM;AACR;AAEA,IAAMkL,YAAA,GAAe;EACnB,CAACrL,UAAU,GAAGiL,mBAAA;EACd,CAAChL,IAAI,GAAGkL;AACV;AAEA,SAASG,WAAWC,SAAA,EAAgB;EAElC,IAAI9K,MAAA,CAAO8K,SAAS,GAAG;IACrB,OAAOJ,YAAA;EACT;EAGA,OAAOE,YAAA,CAAaE,SAAA,CAAU,UAAU,CAAC,KAAKrB,aAAA;AAChD;AAkBA,IAAMsB,cAAA,GAAiBrK,MAAA,CAAOqK,cAAA;AAC9B,IAAMC,mBAAA,GAAsBtK,MAAA,CAAOsK,mBAAA;AACnC,IAAMC,qBAAA,GAAwBvK,MAAA,CAAOuK,qBAAA;AACrC,IAAMC,wBAAA,GAA2BxK,MAAA,CAAOwK,wBAAA;AACxC,IAAMrH,cAAA,GAAiBnD,MAAA,CAAOmD,cAAA;AAC9B,IAAMsH,eAAA,GAAkBzK,MAAA,CAAOC,SAAA;AAEhB,SAARyK,qBAOLC,eAAA,EACAC,eAAA,EACgD;EAChD,IAAI,OAAOA,eAAA,KAAoB,UAAU;IAGvC,IAAIH,eAAA,EAAiB;MACnB,MAAMI,kBAAA,GAAqB1H,cAAA,CAAeyH,eAAe;MACzD,IAAIC,kBAAA,IAAsBA,kBAAA,KAAuBJ,eAAA,EAAiB;QAChEC,oBAAA,CAAqBC,eAAA,EAAiBE,kBAAkB;MAC1D;IACF;IAEA,IAAIjC,IAAA,GAA4B0B,mBAAA,CAAoBM,eAAe;IAEnE,IAAIL,qBAAA,EAAuB;MACzB3B,IAAA,GAAOA,IAAA,CAAKkC,MAAA,CAAOP,qBAAA,CAAsBK,eAAe,CAAC;IAC3D;IAEA,MAAMG,aAAA,GAAgBZ,UAAA,CAAWQ,eAAe;IAChD,MAAMK,aAAA,GAAgBb,UAAA,CAAWS,eAAe;IAEhD,SAAS9B,CAAA,GAAI,GAAGA,CAAA,GAAIF,IAAA,CAAK5E,MAAA,EAAQ,EAAE8E,CAAA,EAAG;MACpC,MAAMjG,GAAA,GAAM+F,IAAA,CAAKE,CAAC;MAClB,IACE,CAACW,aAAA,CAAc5G,GAAiC,KAChD,EAAEmI,aAAA,IAAiBA,aAAA,CAAcnI,GAAiC,MAClE,EAAEkI,aAAA,IAAiBA,aAAA,CAAclI,GAAiC,IAClE;QACA,MAAMoI,UAAA,GAAaT,wBAAA,CAAyBI,eAAA,EAAiB/H,GAAG;QAChE,IAAI;UAEFwH,cAAA,CAAeM,eAAA,EAAiB9H,GAAA,EAAKoI,UAAW;QAClD,SAASrL,CAAA,EAAG,CAEZ;MACF;IACF;EACF;EAEA,OAAO+K,eAAA;AACT;;;AC3HA,IAAMO,UAAA,GAA6B,eAAApN,MAAA,CAAOC,GAAA,CAAI,qBAAqB;AACnE,IAAMoN,EAAA,GAMJ,OAAOC,UAAA,KAAe,cAClBA,UAAA;AAC2F,CAAC;AAGlG,SAASC,WAAA,EAAqD;EAC5D,IAAI,CAAC5N,KAAA,CAAM6N,aAAA,EAAe,OAAO,CAAC;EAElC,MAAMC,UAAA,GAAcJ,EAAA,CAAGD,UAAU,MAAM,mBAAIM,GAAA,CAGzC;EACF,IAAIC,WAAA,GAAcF,UAAA,CAAWxF,GAAA,CAAItI,KAAA,CAAM6N,aAAa;EACpD,IAAI,CAACG,WAAA,EAAa;IAChBA,WAAA,GAAchO,KAAA,CAAM6N,aAAA,CAClB,IACF;IACA,IAAI/I,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzCgJ,WAAA,CAAYlI,WAAA,GAAc;IAC5B;IACAgI,UAAA,CAAWG,GAAA,CAAIjO,KAAA,CAAM6N,aAAA,EAAeG,WAAW;EACjD;EACA,OAAOA,WAAA;AACT;AAEO,IAAME,iBAAA,GAAkC,eAAAN,UAAA,CAAW;;;ACJ1D,IAAMO,qBAAA,GAAwB,CAAC,MAAM,IAAI;AAIzC,IAAMC,kBAAA,GAAsBC,IAAA,IAAkB;EAC5C,IAAI;IACF,OAAOC,IAAA,CAAKC,SAAA,CAAUF,IAAI;EAC5B,SAASG,GAAA,EAAK;IACZ,OAAOC,MAAA,CAAOJ,IAAI;EACpB;AACF;AAQA,SAASK,kCACPC,UAAA,EACAC,UAAA,EACAC,YAAA,EACA;EACAlE,yBAAA,CAA0B,MAAMgE,UAAA,CAAW,GAAGC,UAAU,GAAGC,YAAY;AACzE;AAGA,SAASC,oBACPC,gBAAA,EACAC,cAAA,EACAC,iBAAA,EACAC,YAAA,EAEAC,yBAAA,EACA3F,gBAAA,EACA;EAEAuF,gBAAA,CAAiBK,OAAA,GAAUF,YAAA;EAC3BD,iBAAA,CAAkBG,OAAA,GAAU;EAG5B,IAAID,yBAAA,CAA0BC,OAAA,EAAS;IACrCD,yBAAA,CAA0BC,OAAA,GAAU;IACpC5F,gBAAA,CAAiB;EACnB;AACF;AAIA,SAAS6F,iBACPC,wBAAA,EACAvG,KAAA,EACAW,YAAA,EACA6F,kBAAA,EACAR,gBAAA,EACAC,cAAA,EACAC,iBAAA,EACAO,SAAA,EACAL,yBAAA,EACA3F,gBAAA,EAEAiG,2BAAA,EACA;EAEA,IAAI,CAACH,wBAAA,EAA0B,OAAO,MAAM,CAAC;EAG7C,IAAII,cAAA,GAAiB;EACrB,IAAIC,eAAA,GAAgC;EAGpC,MAAMC,eAAA,GAAkBA,CAAA,KAAM;IAC5B,IAAIF,cAAA,IAAkB,CAACF,SAAA,CAAUJ,OAAA,EAAS;MAGxC;IACF;IAGA,MAAMS,gBAAA,GAAmB9G,KAAA,CAAM+G,QAAA,CAAS;IAExC,IAAIC,aAAA,EAAe9N,KAAA;IACnB,IAAI;MAGF8N,aAAA,GAAgBR,kBAAA,CACdM,gBAAA,EACAd,gBAAA,CAAiBK,OACnB;IACF,SAASjN,CAAA,EAAG;MACVF,KAAA,GAAQE,CAAA;MACRwN,eAAA,GAAkBxN,CAAA;IACpB;IAEA,IAAI,CAACF,KAAA,EAAO;MACV0N,eAAA,GAAkB;IACpB;IAGA,IAAII,aAAA,KAAkBf,cAAA,CAAeI,OAAA,EAAS;MAC5C,IAAI,CAACH,iBAAA,CAAkBG,OAAA,EAAS;QAC9B5F,gBAAA,CAAiB;MACnB;IACF,OAAO;MAKLwF,cAAA,CAAeI,OAAA,GAAUW,aAAA;MACzBZ,yBAAA,CAA0BC,OAAA,GAAUW,aAAA;MACpCd,iBAAA,CAAkBG,OAAA,GAAU;MAI5BK,2BAAA,CAA4B;IAC9B;EACF;EAGA/F,YAAA,CAAaC,aAAA,GAAgBiG,eAAA;EAC7BlG,YAAA,CAAaN,YAAA,CAAa;EAI1BwG,eAAA,CAAgB;EAEhB,MAAMI,kBAAA,GAAqBA,CAAA,KAAM;IAC/BN,cAAA,GAAiB;IACjBhG,YAAA,CAAaH,cAAA,CAAe;IAC5BG,YAAA,CAAaC,aAAA,GAAgB;IAE7B,IAAIgG,eAAA,EAAiB;MAMnB,MAAMA,eAAA;IACR;EACF;EAEA,OAAOK,kBAAA;AACT;AAgBA,SAASC,YAAYC,CAAA,EAAYC,CAAA,EAAY;EAC3C,OAAOD,CAAA,KAAMC,CAAA;AACf;AAmNA,IAAIC,kCAAA,GAAqC;AAsBzC,SAASC,QAOPzN,eAAA,EACAC,kBAAA,EACAC,UAAA,EACA;EAAA;EAAA;EAGEwN,IAAA;EACArN,cAAA,GAAiBgN,WAAA;EACjB/M,gBAAA,GAAmB6H,YAAA;EACnB5H,kBAAA,GAAqB4H,YAAA;EACrBvD,mBAAA,GAAsBuD,YAAA;EAAA;EAGtBwF,UAAA,GAAa;EAAA;EAGbC,OAAA,GAAUtC;AACZ,IAAwD,CAAC,GAChD;EACT,IAAIpJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAIsL,IAAA,KAAS,UAAa,CAACF,kCAAA,EAAoC;MAC7DA,kCAAA,GAAqC;MACrCtO,OAAA,CACE,yFACF;IACF;EACF;EAEA,MAAM2O,OAAA,GAAUD,OAAA;EAEhB,MAAM9L,mBAAA,GAAsB0C,sBAAA,CAAuBxE,eAAe;EAClE,MAAM+B,sBAAA,GAAyBwC,yBAAA,CAA0BtE,kBAAkB;EAC3E,MAAM+B,cAAA,GAAiBgD,iBAAA,CAAkB9E,UAAU;EAEnD,MAAMwM,wBAAA,GAA2BhJ,OAAA,CAAQ1D,eAAe;EAExD,MAAM8N,eAAA,GACJC,gBAAA,IACG;IAIH,IAAI7L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,MAAM4L,OAAA,GAAwB,eAAAtP,kBAAA,CAAmBqP,gBAAgB;MACjE,IAAI,CAACC,OAAA,EACH,MAAM,IAAI1O,KAAA,CACR,mFAAmFkM,kBAAA,CACjFuC,gBACF,CAAC,EACH;IACJ;IAEA,MAAMzJ,oBAAA,GACJyJ,gBAAA,CAAiB7K,WAAA,IAAe6K,gBAAA,CAAiB1J,IAAA,IAAQ;IAE3D,MAAMnB,WAAA,GAAc,WAAWoB,oBAAoB;IAEnD,MAAM2J,sBAAA,GAMF;MACFvB,wBAAA;MACAxJ,WAAA;MACAoB,oBAAA;MACAyJ,gBAAA;MAAA;MAEAjM,mBAAA;MACAC,sBAAA;MACAC,cAAA;MACA3B,cAAA;MACAE,kBAAA;MACAD,gBAAA;MACAsE;IACF;IAEA,SAASsJ,gBACPhK,KAAA,EACA;MACA,MAAM,CAACiK,YAAA,EAAcC,sBAAA,EAAwB9B,YAAY,IACvDlP,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QAIlB,MAAM;UAAED,sBAAA,EAAAE,uBAAA;UAAwB,GAAGC;QAAa,IAAIrK,KAAA;QACpD,OAAO,CAACA,KAAA,CAAM0J,OAAA,EAASU,uBAAA,EAAwBC,aAAY;MAC7D,GAAG,CAACrK,KAAK,CAAC;MAEZ,MAAMsK,YAAA,GAA0CpR,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QAGlE,IAAII,aAAA,GAAgBZ,OAAA;QACpB,IAAIM,YAAA,EAAcO,QAAA,EAAU;UAC1B,IAAIxM,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;YACzC,MAAM4L,OAAA,GAAwB,eAAAhP,iBAAA;YAAA;YAE5B;YAAA5B,KAAA,CAAAkK,aAAA,CAAC6G,YAAA,CAAaO,QAAA,EAAb,IAAsB,CACzB;YACA,IAAI,CAACV,OAAA,EAAS;cACZ,MAAM,IAAI1O,KAAA,CACR,iEACF;YACF;YACAmP,aAAA,GAAgBN,YAAA;UAClB;QACF;QACA,OAAOM,aAAA;MACT,GAAG,CAACN,YAAA,EAAcN,OAAO,CAAC;MAG1B,MAAMc,YAAA,GAAevR,KAAA,CAAMwR,UAAA,CAAWJ,YAAY;MAKlD,MAAMK,qBAAA,GACJnL,OAAA,CAAQQ,KAAA,CAAMiC,KAAK,KACnBzC,OAAA,CAAQQ,KAAA,CAAMiC,KAAA,CAAO+G,QAAQ,KAC7BxJ,OAAA,CAAQQ,KAAA,CAAMiC,KAAA,CAAO/F,QAAQ;MAC/B,MAAM0O,uBAAA,GACJpL,OAAA,CAAQiL,YAAY,KAAKjL,OAAA,CAAQiL,YAAA,CAAcxI,KAAK;MAEtD,IACEjE,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBACzB,CAACyM,qBAAA,IACD,CAACC,uBAAA,EACD;QACA,MAAM,IAAIxP,KAAA,CACR,6CACM4D,WAAW,4JAEcA,WAAW,sBAC5C;MACF;MAGA,MAAMiD,KAAA,GAAe0I,qBAAA,GACjB3K,KAAA,CAAMiC,KAAA,GACNwI,YAAA,CAAcxI,KAAA;MAElB,MAAM4I,cAAA,GAAiBD,uBAAA,GACnBH,YAAA,CAAcI,cAAA,GACd5I,KAAA,CAAM+G,QAAA;MAEV,MAAMP,kBAAA,GAAqBvP,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QAG7C,OAAOxM,yBAAA,CAAuBsE,KAAA,CAAM/F,QAAA,EAAU6N,sBAAsB;MACtE,GAAG,CAAC9H,KAAK,CAAC;MAEV,MAAM,CAACW,YAAA,EAAcF,gBAAgB,IAAIxJ,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QAC3D,IAAI,CAAC3B,wBAAA,EAA0B,OAAOnB,qBAAA;QAItC,MAAMyD,aAAA,GAAe9I,kBAAA,CACnBC,KAAA,EACA0I,qBAAA,GAAwB,SAAYF,YAAA,CAAc7H,YACpD;QAMA,MAAMmI,iBAAA,GACJD,aAAA,CAAapI,gBAAA,CAAiBsI,IAAA,CAAKF,aAAY;QAEjD,OAAO,CAACA,aAAA,EAAcC,iBAAgB;MACxC,GAAG,CAAC9I,KAAA,EAAO0I,qBAAA,EAAuBF,YAAY,CAAC;MAI/C,MAAMQ,sBAAA,GAAyB/R,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QACjD,IAAIQ,qBAAA,EAAuB;UAIzB,OAAOF,YAAA;QACT;QAIA,OAAO;UACL,GAAGA,YAAA;UACH7H;QACF;MACF,GAAG,CAAC+H,qBAAA,EAAuBF,YAAA,EAAc7H,YAAY,CAAC;MAGtD,MAAMsF,cAAA,GAAiBhP,KAAA,CAAMgS,MAAA,CAAgB,MAAS;MACtD,MAAMjD,gBAAA,GAAmB/O,KAAA,CAAMgS,MAAA,CAAO9C,YAAY;MAClD,MAAMC,yBAAA,GAA4BnP,KAAA,CAAMgS,MAAA,CAAgB,MAAS;MACjE,MAAM/C,iBAAA,GAAoBjP,KAAA,CAAMgS,MAAA,CAAO,KAAK;MAC5C,MAAMxC,SAAA,GAAYxP,KAAA,CAAMgS,MAAA,CAAO,KAAK;MAMpC,MAAMC,+BAAA,GAAkCjS,KAAA,CAAMgS,MAAA,CAC5C,MACF;MAEArH,yBAAA,CAA0B,MAAM;QAC9B6E,SAAA,CAAUJ,OAAA,GAAU;QACpB,OAAO,MAAM;UACXI,SAAA,CAAUJ,OAAA,GAAU;QACtB;MACF,GAAG,EAAE;MAEL,MAAM8C,wBAAA,GAA2BlS,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QACnD,MAAM5O,QAAA,GAAWA,CAAA,KAAM;UAOrB,IACE8M,yBAAA,CAA0BC,OAAA,IAC1BF,YAAA,KAAiBH,gBAAA,CAAiBK,OAAA,EAClC;YACA,OAAOD,yBAAA,CAA0BC,OAAA;UACnC;UAMA,OAAOG,kBAAA,CAAmBxG,KAAA,CAAM+G,QAAA,CAAS,GAAGZ,YAAY;QAC1D;QACA,OAAO7M,QAAA;MACT,GAAG,CAAC0G,KAAA,EAAOmG,YAAY,CAAC;MAMxB,MAAMiD,iBAAA,GAAoBnS,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QAC5C,MAAMxI,SAAA,GAAa2J,aAAA,IAA8B;UAC/C,IAAI,CAAC1I,YAAA,EAAc;YACjB,OAAO,MAAM,CAAC;UAChB;UAEA,OAAO2F,gBAAA,CACLC,wBAAA,EACAvG,KAAA,EACAW,YAAA;UAAA;UAEA6F,kBAAA,EACAR,gBAAA,EACAC,cAAA,EACAC,iBAAA,EACAO,SAAA,EACAL,yBAAA,EACA3F,gBAAA,EACA4I,aACF;QACF;QAEA,OAAO3J,SAAA;MACT,GAAG,CAACiB,YAAY,CAAC;MAEjBgF,iCAAA,CAAkCI,mBAAA,EAAqB,CACrDC,gBAAA,EACAC,cAAA,EACAC,iBAAA,EACAC,YAAA,EACAC,yBAAA,EACA3F,gBAAA,CACD;MAED,IAAI6I,gBAAA;MAEJ,IAAI;QACFA,gBAAA,GAAmBrS,KAAA,CAAMsS,oBAAA;QAAA;QAEvBH,iBAAA;QAAA;QAAA;QAGAD,wBAAA,EACAP,cAAA,GACI,MAAMpC,kBAAA,CAAmBoC,cAAA,CAAe,GAAGzC,YAAY,IACvDgD,wBACN;MACF,SAAS1D,GAAA,EAAK;QACZ,IAAIyD,+BAAA,CAAgC7C,OAAA,EAAS;UAE3C;UAAEZ,GAAA,CAAczM,OAAA,IACd;AAAA;AAAA,EAA4DkQ,+BAAA,CAAgC7C,OAAA,CAAQmD,KAAK;AAAA;AAAA;QAC7G;QAEA,MAAM/D,GAAA;MACR;MAEA7D,yBAAA,CAA0B,MAAM;QAC9BsH,+BAAA,CAAgC7C,OAAA,GAAU;QAC1CD,yBAAA,CAA0BC,OAAA,GAAU;QACpCJ,cAAA,CAAeI,OAAA,GAAUiD,gBAAA;MAC3B,CAAC;MAID,MAAMG,wBAAA,GAA2BxS,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QACnD;UAAA;UAEE;UAAAjR,KAAA,CAAAkK,aAAA,CAACyG,gBAAA;YACE,GAAG0B,gBAAA;YACJI,GAAA,EAAKzB;UAAA,CACP;QAAA;MAEJ,GAAG,CAACA,sBAAA,EAAwBL,gBAAA,EAAkB0B,gBAAgB,CAAC;MAI/D,MAAMK,aAAA,GAAgB1S,KAAA,CAAMiR,OAAA,CAAQ,MAAM;QACxC,IAAI3B,wBAAA,EAA0B;UAI5B,OACE,eAAAtP,KAAA,CAAAkK,aAAA,CAACkH,YAAA,CAAauB,QAAA,EAAb;YAAsB9M,KAAA,EAAOkM;UAAA,GAC3BS,wBACH;QAEJ;QAEA,OAAOA,wBAAA;MACT,GAAG,CAACpB,YAAA,EAAcoB,wBAAA,EAA0BT,sBAAsB,CAAC;MAEnE,OAAOW,aAAA;IACT;IAEA,MAAME,QAAA,GAAW5S,KAAA,CAAM6S,IAAA,CAAK/B,eAAe;IAO3C,MAAMgC,OAAA,GAAUF,QAAA;IAIhBE,OAAA,CAAQnC,gBAAA,GAAmBA,gBAAA;IAC3BmC,OAAA,CAAQhN,WAAA,GAAcgL,eAAA,CAAgBhL,WAAA,GAAcA,WAAA;IAEpD,IAAIyK,UAAA,EAAY;MACd,MAAMwC,UAAA,GAAa/S,KAAA,CAAMuQ,UAAA,CACvB,SAASyC,kBAAkBlM,KAAA,EAAO2L,GAAA,EAAK;QAErC,OAAO,eAAAzS,KAAA,CAAAkK,aAAA,CAAC4I,OAAA;UAAS,GAAGhM,KAAA;UAAOkK,sBAAA,EAAwByB;QAAA,CAAK;MAC1D,CACF;MAEA,MAAMQ,SAAA,GAAYF,UAAA;MAClBE,SAAA,CAAUnN,WAAA,GAAcA,WAAA;MACxBmN,SAAA,CAAUtC,gBAAA,GAAmBA,gBAAA;MAC7B,OAAqB,eAAA1D,oBAAA,CAAagG,SAAA,EAAWtC,gBAAgB;IAC/D;IAEA,OAAqB,eAAA1D,oBAAA,CAAa6F,OAAA,EAASnC,gBAAgB;EAC7D;EAEA,OAAOD,eAAA;AACT;AAEA,IAAOwC,eAAA,GAAQ7C,OAAA;;;ACpvBf,SAASsC,SACPQ,aAAA,EACA;EACA,MAAM;IAAEC,QAAA;IAAU5C,OAAA;IAAS6C,WAAA;IAAatK;EAAM,IAAIoK,aAAA;EAElD,MAAM5B,YAAA,GAAevR,KAAA,CAAMiR,OAAA,CAAQ,MAAM;IACvC,MAAMvH,YAAA,GAAeZ,kBAAA,CAAmBC,KAAK;IAE7C,MAAMuK,gBAAA,GAAmB;MACvBvK,KAAA;MACAW,YAAA;MACAiI,cAAA,EAAgB0B,WAAA,GAAc,MAAMA,WAAA,GAAc;IACpD;IAEA,IAAIvO,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,OAAOsO,gBAAA;IACT,OAAO;MACL,MAAM;QAAEC,qBAAA,GAAwB;QAAQC,cAAA,GAAiB;MAAO,IAC9DL,aAAA;MAEF,OAAuB,eAAA5Q,MAAA,CAAOkR,MAAA,CAAOH,gBAAA,EAAkB;QACrDE,cAAA;QACAD;MACF,CAAC;IACH;EACF,GAAG,CAACxK,KAAA,EAAOsK,WAAW,CAAC;EAEvB,MAAMK,aAAA,GAAgB1T,KAAA,CAAMiR,OAAA,CAAQ,MAAMlI,KAAA,CAAM+G,QAAA,CAAS,GAAG,CAAC/G,KAAK,CAAC;EAEnE4B,yBAAA,CAA0B,MAAM;IAC9B,MAAM;MAAEjB;IAAa,IAAI6H,YAAA;IACzB7H,YAAA,CAAaC,aAAA,GAAgBD,YAAA,CAAaF,gBAAA;IAC1CE,YAAA,CAAaN,YAAA,CAAa;IAE1B,IAAIsK,aAAA,KAAkB3K,KAAA,CAAM+G,QAAA,CAAS,GAAG;MACtCpG,YAAA,CAAaF,gBAAA,CAAiB;IAChC;IACA,OAAO,MAAM;MACXE,YAAA,CAAaH,cAAA,CAAe;MAC5BG,YAAA,CAAaC,aAAA,GAAgB;IAC/B;EACF,GAAG,CAAC4H,YAAA,EAAcmC,aAAa,CAAC;EAEhC,MAAMjD,OAAA,GAAUD,OAAA,IAAWtC,iBAAA;EAE3B,OAAO,eAAAlO,KAAA,CAAAkK,aAAA,CAACuG,OAAA,CAAQkC,QAAA,EAAR;IAAiB9M,KAAA,EAAO0L;EAAA,GAAe6B,QAAS;AAC1D;AAEA,IAAOO,gBAAA,GAAQhB,QAAA;;;AC7FR,SAASiB,uBAAuBpD,OAAA,GAAUtC,iBAAA,EAAmB;EAClE,OAAO,SAAS2F,iBAAA,EAA0C;IACxD,MAAMtC,YAAA,GAAevR,KAAA,CAAMwR,UAAA,CAAWhB,OAAO;IAE7C,IAAI1L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,CAACuM,YAAA,EAAc;MAC1D,MAAM,IAAIrP,KAAA,CACR,kGACF;IACF;IAEA,OAAOqP,YAAA;EACT;AACF;AAkBO,IAAMuC,eAAA,GAAgC,eAAAF,sBAAA,CAAuB;;;ACuC7D,SAASG,gBAKdvD,OAAA,GAGYtC,iBAAA,EACZ;EACA,MAAM2F,gBAAA,GACJrD,OAAA,KAAYtC,iBAAA,GACR4F,eAAA;EAAA;EAEAF,sBAAA,CAAuBpD,OAAO;EACpC,MAAMwD,SAAA,GAAWC,CAAA,KAAM;IACrB,MAAM;MAAElL;IAAM,IAAI8K,gBAAA,CAAgB;IAClC,OAAO9K,KAAA;EACT;EAEAxG,MAAA,CAAOkR,MAAA,CAAOO,SAAA,EAAU;IACtBE,SAAA,EAAWA,CAAA,KAAMF;EACnB,CAAC;EAED,OAAOA,SAAA;AACT;AAiBO,IAAMC,QAAA,GAAyB,eAAAF,eAAA,CAAgB;;;ACjE/C,SAASI,mBAKd3D,OAAA,GAGYtC,iBAAA,EACZ;EACA,MAAM8F,SAAA,GACJxD,OAAA,KAAYtC,iBAAA,GAAoB+F,QAAA,GAAkBF,eAAA,CAAgBvD,OAAO;EAE3E,MAAM4D,YAAA,GAAcC,CAAA,KAAM;IACxB,MAAMtL,KAAA,GAAQiL,SAAA,CAAS;IACvB,OAAOjL,KAAA,CAAM/F,QAAA;EACf;EAEAT,MAAA,CAAOkR,MAAA,CAAOW,YAAA,EAAa;IACzBF,SAAA,EAAWA,CAAA,KAAME;EACnB,CAAC;EAED,OAAOA,YAAA;AACT;AAuBO,IAAMC,WAAA,GAA4B,eAAAF,kBAAA,CAAmB;;;ACrG5D,SAASG,gCAAA,QAAwC;AAoHjD,IAAMC,WAAA,GAA+BA,CAACrE,CAAA,EAAGC,CAAA,KAAMD,CAAA,KAAMC,CAAA;AAQ9C,SAASqE,mBACdhE,OAAA,GAGYtC,iBAAA,EACC;EACb,MAAM2F,gBAAA,GACJrD,OAAA,KAAYtC,iBAAA,GACR4F,eAAA,GACAF,sBAAA,CAAuBpD,OAAO;EAEpC,MAAMiE,YAAA,GAAcC,CAClBrS,QAAA,EACAsS,mBAAA,GAE4C,CAAC,MAChC;IACb,MAAM;MAAEC,UAAA,GAAaL;IAAY,IAC/B,OAAOI,mBAAA,KAAwB,aAC3B;MAAEC,UAAA,EAAYD;IAAoB,IAClCA,mBAAA;IACN,IAAI7P,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,IAAI,CAAC3C,QAAA,EAAU;QACb,MAAM,IAAIH,KAAA,CAAM,yCAAyC;MAC3D;MACA,IAAI,OAAOG,QAAA,KAAa,YAAY;QAClC,MAAM,IAAIH,KAAA,CAAM,uDAAuD;MACzE;MACA,IAAI,OAAO0S,UAAA,KAAe,YAAY;QACpC,MAAM,IAAI1S,KAAA,CACR,iEACF;MACF;IACF;IAEA,MAAM2S,YAAA,GAAehB,gBAAA,CAAgB;IAErC,MAAM;MAAE9K,KAAA;MAAOW,YAAA;MAAciI;IAAe,IAAIkD,YAAA;IAEhD,MAAMC,QAAA,GAAW9U,KAAA,CAAMgS,MAAA,CAAO,IAAI;IAElC,MAAM+C,eAAA,GAAkB/U,KAAA,CAAMgV,WAAA,CAC5B;MACE,CAAC3S,QAAA,CAAS4E,IAAI,EAAE5D,KAAA,EAAe;QAC7B,MAAM4R,QAAA,GAAW5S,QAAA,CAASgB,KAAK;QAC/B,IAAIyB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;UACzC,MAAM;YAAEkQ,aAAA,GAAgB,CAAC;UAAE,IACzB,OAAOP,mBAAA,KAAwB,aAC3B,CAAC,IACDA,mBAAA;UACN,MAAM;YAAEpB,qBAAA;YAAuBC;UAAe,IAAIqB,YAAA;UAClD,MAAM;YACJtB,qBAAA,EAAuB4B,0BAAA;YACvB3B,cAAA,EAAgB4B;UAClB,IAAI;YACF5B,cAAA;YACAD,qBAAA;YACA,GAAG2B;UACL;UACA,IACEE,mBAAA,KAAwB,YACvBA,mBAAA,KAAwB,UAAUN,QAAA,CAAS1F,OAAA,EAC5C;YACA,MAAMiG,SAAA,GAAYhT,QAAA,CAASgB,KAAK;YAChC,IAAI,CAACuR,UAAA,CAAWK,QAAA,EAAUI,SAAS,GAAG;cACpC,IAAI9C,KAAA,GAA4B;cAChC,IAAI;gBACF,MAAM,IAAIrQ,KAAA,CAAM;cAClB,SAASC,CAAA,EAAG;gBAEV;gBAAC,CAAC;kBAAEoQ;gBAAM,IAAIpQ,CAAA;cAChB;cACAH,OAAA,CAAQsT,IAAA,CACN,eACGjT,QAAA,CAAS4E,IAAA,IAAQ,aAClB,kSAEF;gBACE5D,KAAA;gBACA4R,QAAA;gBACAM,SAAA,EAAWF,SAAA;gBACX9C;cACF,CACF;YACF;UACF;UACA,IACE4C,0BAAA,KAA+B,YAC9BA,0BAAA,KAA+B,UAAUL,QAAA,CAAS1F,OAAA,EACnD;YAEA,IAAI6F,QAAA,KAAa5R,KAAA,EAAO;cACtB,IAAIkP,KAAA,GAA4B;cAChC,IAAI;gBACF,MAAM,IAAIrQ,KAAA,CAAM;cAClB,SAASC,CAAA,EAAG;gBAEV;gBAAC,CAAC;kBAAEoQ;gBAAM,IAAIpQ,CAAA;cAChB;cACAH,OAAA,CAAQsT,IAAA,CACN,eACGjT,QAAA,CAAS4E,IAAA,IAAQ,aAClB,6NAEF;gBAAEsL;cAAM,CACV;YACF;UACF;UACA,IAAIuC,QAAA,CAAS1F,OAAA,EAAS0F,QAAA,CAAS1F,OAAA,GAAU;QAC3C;QACA,OAAO6F,QAAA;MACT;IACF,EAAE5S,QAAA,CAAS4E,IAAI,GACf,CAAC5E,QAAQ,CACX;IAEA,MAAMmT,aAAA,GAAgBlB,gCAAA,CACpB5K,YAAA,CAAaP,YAAA,EACbJ,KAAA,CAAM+G,QAAA,EACN6B,cAAA,IAAkB5I,KAAA,CAAM+G,QAAA,EACxBiF,eAAA,EACAH,UACF;IAEA5U,KAAA,CAAMyV,aAAA,CAAcD,aAAa;IAEjC,OAAOA,aAAA;EACT;EAEAjT,MAAA,CAAOkR,MAAA,CAAOgB,YAAA,EAAa;IACzBP,SAAA,EAAWA,CAAA,KAAMO;EACnB,CAAC;EAED,OAAOA,YAAA;AACT;AAyBO,IAAMC,WAAA,GAA4B,eAAAF,kBAAA,CAAmB;;;AC7O5D,IAAMkB,KAAA,GAAQ7N,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}