{"ast": null, "code": "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport { tau } from \"./math.js\";\nexport default function () {\n  var value = identity,\n    sortValues = descending,\n    sort = null,\n    startAngle = constant(0),\n    endAngle = constant(tau),\n    padAngle = constant(0);\n  function pie(data) {\n    var i,\n      n = (data = array(data)).length,\n      j,\n      k,\n      sum = 0,\n      index = new Array(n),\n      arcs = new Array(n),\n      a0 = +startAngle.apply(this, arguments),\n      da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n      a1,\n      p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n      pa = p * (da < 0 ? -1 : 1),\n      v;\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function (i, j) {\n      return sortValues(arcs[i], arcs[j]);\n    });else if (sort != null) index.sort(function (i, j) {\n      return sort(data[i], data[j]);\n    });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n    return arcs;\n  }\n  pie.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n  pie.sortValues = function (_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n  pie.sort = function (_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n  pie.startAngle = function (_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n  pie.endAngle = function (_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n  pie.padAngle = function (_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n  return pie;\n}", "map": {"version": 3, "names": ["array", "constant", "descending", "identity", "tau", "value", "sortValues", "sort", "startAngle", "endAngle", "padAngle", "pie", "data", "i", "n", "length", "j", "k", "sum", "index", "Array", "arcs", "a0", "apply", "arguments", "da", "Math", "min", "max", "a1", "p", "abs", "pa", "v", "_"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-shape/src/pie.js"], "sourcesContent": ["import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport {tau} from \"./math.js\";\n\nexport default function() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = (data = array(data)).length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAAQC,GAAG,QAAO,WAAW;AAE7B,eAAe,YAAW;EACxB,IAAIC,KAAK,GAAGF,QAAQ;IAChBG,UAAU,GAAGJ,UAAU;IACvBK,IAAI,GAAG,IAAI;IACXC,UAAU,GAAGP,QAAQ,CAAC,CAAC,CAAC;IACxBQ,QAAQ,GAAGR,QAAQ,CAACG,GAAG,CAAC;IACxBM,QAAQ,GAAGT,QAAQ,CAAC,CAAC,CAAC;EAE1B,SAASU,GAAGA,CAACC,IAAI,EAAE;IACjB,IAAIC,CAAC;MACDC,CAAC,GAAG,CAACF,IAAI,GAAGZ,KAAK,CAACY,IAAI,CAAC,EAAEG,MAAM;MAC/BC,CAAC;MACDC,CAAC;MACDC,GAAG,GAAG,CAAC;MACPC,KAAK,GAAG,IAAIC,KAAK,CAACN,CAAC,CAAC;MACpBO,IAAI,GAAG,IAAID,KAAK,CAACN,CAAC,CAAC;MACnBQ,EAAE,GAAG,CAACd,UAAU,CAACe,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACvCC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACvB,GAAG,EAAEsB,IAAI,CAACE,GAAG,CAAC,CAACxB,GAAG,EAAEK,QAAQ,CAACc,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGF,EAAE,CAAC,CAAC;MACxEO,EAAE;MACFC,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACN,EAAE,CAAC,GAAGX,CAAC,EAAEJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;MAC/DQ,EAAE,GAAGF,CAAC,IAAIL,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1BQ,CAAC;IAEL,KAAKpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtB,IAAI,CAACoB,CAAC,GAAGZ,IAAI,CAACF,KAAK,CAACN,CAAC,CAAC,GAAGA,CAAC,CAAC,GAAG,CAACR,KAAK,CAACO,IAAI,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3DM,GAAG,IAAIe,CAAC;MACV;IACF;;IAEA;IACA,IAAI3B,UAAU,IAAI,IAAI,EAAEa,KAAK,CAACZ,IAAI,CAAC,UAASM,CAAC,EAAEG,CAAC,EAAE;MAAE,OAAOV,UAAU,CAACe,IAAI,CAACR,CAAC,CAAC,EAAEQ,IAAI,CAACL,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC,KACvF,IAAIT,IAAI,IAAI,IAAI,EAAEY,KAAK,CAACZ,IAAI,CAAC,UAASM,CAAC,EAAEG,CAAC,EAAE;MAAE,OAAOT,IAAI,CAACK,IAAI,CAACC,CAAC,CAAC,EAAED,IAAI,CAACI,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;;IAEpF;IACA,KAAKH,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAGC,GAAG,GAAG,CAACO,EAAE,GAAGX,CAAC,GAAGkB,EAAE,IAAId,GAAG,GAAG,CAAC,EAAEL,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAES,EAAE,GAAGO,EAAE,EAAE;MAClEb,CAAC,GAAGG,KAAK,CAACN,CAAC,CAAC,EAAEoB,CAAC,GAAGZ,IAAI,CAACL,CAAC,CAAC,EAAEa,EAAE,GAAGP,EAAE,IAAIW,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGhB,CAAC,GAAG,CAAC,CAAC,GAAGe,EAAE,EAAEX,IAAI,CAACL,CAAC,CAAC,GAAG;QACvEJ,IAAI,EAAEA,IAAI,CAACI,CAAC,CAAC;QACbG,KAAK,EAAEN,CAAC;QACRR,KAAK,EAAE4B,CAAC;QACRzB,UAAU,EAAEc,EAAE;QACdb,QAAQ,EAAEoB,EAAE;QACZnB,QAAQ,EAAEoB;MACZ,CAAC;IACH;IAEA,OAAOT,IAAI;EACb;EAEAV,GAAG,CAACN,KAAK,GAAG,UAAS6B,CAAC,EAAE;IACtB,OAAOV,SAAS,CAACT,MAAM,IAAIV,KAAK,GAAG,OAAO6B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjC,QAAQ,CAAC,CAACiC,CAAC,CAAC,EAAEvB,GAAG,IAAIN,KAAK;EAC7F,CAAC;EAEDM,GAAG,CAACL,UAAU,GAAG,UAAS4B,CAAC,EAAE;IAC3B,OAAOV,SAAS,CAACT,MAAM,IAAIT,UAAU,GAAG4B,CAAC,EAAE3B,IAAI,GAAG,IAAI,EAAEI,GAAG,IAAIL,UAAU;EAC3E,CAAC;EAEDK,GAAG,CAACJ,IAAI,GAAG,UAAS2B,CAAC,EAAE;IACrB,OAAOV,SAAS,CAACT,MAAM,IAAIR,IAAI,GAAG2B,CAAC,EAAE5B,UAAU,GAAG,IAAI,EAAEK,GAAG,IAAIJ,IAAI;EACrE,CAAC;EAEDI,GAAG,CAACH,UAAU,GAAG,UAAS0B,CAAC,EAAE;IAC3B,OAAOV,SAAS,CAACT,MAAM,IAAIP,UAAU,GAAG,OAAO0B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjC,QAAQ,CAAC,CAACiC,CAAC,CAAC,EAAEvB,GAAG,IAAIH,UAAU;EACvG,CAAC;EAEDG,GAAG,CAACF,QAAQ,GAAG,UAASyB,CAAC,EAAE;IACzB,OAAOV,SAAS,CAACT,MAAM,IAAIN,QAAQ,GAAG,OAAOyB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjC,QAAQ,CAAC,CAACiC,CAAC,CAAC,EAAEvB,GAAG,IAAIF,QAAQ;EACnG,CAAC;EAEDE,GAAG,CAACD,QAAQ,GAAG,UAASwB,CAAC,EAAE;IACzB,OAAOV,SAAS,CAACT,MAAM,IAAIL,QAAQ,GAAG,OAAOwB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjC,QAAQ,CAAC,CAACiC,CAAC,CAAC,EAAEvB,GAAG,IAAID,QAAQ;EACnG,CAAC;EAED,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}