{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isIndex = require('./isIndex.js');\nconst isArrayLike = require('../predicate/isArrayLike.js');\nconst isObject = require('../predicate/isObject.js');\nconst eq = require('../util/eq.js');\nfunction isIterateeCall(value, index, object) {\n  if (!isObject.isObject(object)) {\n    return false;\n  }\n  if (typeof index === 'number' && isArrayLike.isArrayLike(object) && isIndex.isIndex(index) && index < object.length || typeof index === 'string' && index in object) {\n    return eq.eq(object[index], value);\n  }\n  return false;\n}\nexports.isIterateeCall = isIterateeCall;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isIndex", "require", "isArrayLike", "isObject", "eq", "isIterateeCall", "index", "object", "length"], "sources": ["D:/Desktop/meror/frontend/node_modules/es-toolkit/dist/compat/_internal/isIterateeCall.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isIndex = require('./isIndex.js');\nconst isArrayLike = require('../predicate/isArrayLike.js');\nconst isObject = require('../predicate/isObject.js');\nconst eq = require('../util/eq.js');\n\nfunction isIterateeCall(value, index, object) {\n    if (!isObject.isObject(object)) {\n        return false;\n    }\n    if ((typeof index === 'number' && isArrayLike.isArrayLike(object) && isIndex.isIndex(index) && index < object.length) ||\n        (typeof index === 'string' && index in object)) {\n        return eq.eq(object[index], value);\n    }\n    return false;\n}\n\nexports.isIterateeCall = isIterateeCall;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,WAAW,GAAGD,OAAO,CAAC,6BAA6B,CAAC;AAC1D,MAAME,QAAQ,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAMG,EAAE,GAAGH,OAAO,CAAC,eAAe,CAAC;AAEnC,SAASI,cAAcA,CAACN,KAAK,EAAEO,KAAK,EAAEC,MAAM,EAAE;EAC1C,IAAI,CAACJ,QAAQ,CAACA,QAAQ,CAACI,MAAM,CAAC,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAK,OAAOD,KAAK,KAAK,QAAQ,IAAIJ,WAAW,CAACA,WAAW,CAACK,MAAM,CAAC,IAAIP,OAAO,CAACA,OAAO,CAACM,KAAK,CAAC,IAAIA,KAAK,GAAGC,MAAM,CAACC,MAAM,IAC/G,OAAOF,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAIC,MAAO,EAAE;IAChD,OAAOH,EAAE,CAACA,EAAE,CAACG,MAAM,CAACD,KAAK,CAAC,EAAEP,KAAK,CAAC;EACtC;EACA,OAAO,KAAK;AAChB;AAEAH,OAAO,CAACS,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}