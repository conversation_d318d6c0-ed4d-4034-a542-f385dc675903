{"ast": null, "code": "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}", "map": {"version": 3, "names": ["maxIndex", "values", "valueof", "max", "index", "undefined", "value"], "sources": ["D:/Desktop/meror/frontend/node_modules/d3-array/src/maxIndex.js"], "sourcesContent": ["export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAChD,IAAIC,GAAG;EACP,IAAIH,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAII,KAAK,GAAG,CAAC,CAAC;EACd,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,MAAMC,KAAK,IAAIL,MAAM,EAAE;MAC1B,EAAEG,KAAK;MACP,IAAIE,KAAK,IAAI,IAAI,KACTH,GAAG,GAAGG,KAAK,IAAKH,GAAG,KAAKE,SAAS,IAAIC,KAAK,IAAIA,KAAM,CAAC,EAAE;QAC7DH,GAAG,GAAGG,KAAK,EAAEN,QAAQ,GAAGI,KAAK;MAC/B;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIE,KAAK,IAAIL,MAAM,EAAE;MACxB,IAAI,CAACK,KAAK,GAAGJ,OAAO,CAACI,KAAK,EAAE,EAAEF,KAAK,EAAEH,MAAM,CAAC,KAAK,IAAI,KAC7CE,GAAG,GAAGG,KAAK,IAAKH,GAAG,KAAKE,SAAS,IAAIC,KAAK,IAAIA,KAAM,CAAC,EAAE;QAC7DH,GAAG,GAAGG,KAAK,EAAEN,QAAQ,GAAGI,KAAK;MAC/B;IACF;EACF;EACA,OAAOJ,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}