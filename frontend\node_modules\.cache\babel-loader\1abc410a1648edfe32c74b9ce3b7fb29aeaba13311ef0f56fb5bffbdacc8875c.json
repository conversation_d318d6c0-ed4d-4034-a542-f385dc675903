{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16.23 7h4.12L10.5 2 2 6.21V17h2V7.4L10.5 4z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M5 8v13h17V8zm15 4-6.5 3.33L7 12v-2l6.5 3.33L20 10z\"\n}, \"1\")], 'MarkAsUnreadSharp');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["D:/Desktop/meror/frontend/node_modules/@mui/icons-material/esm/MarkAsUnreadSharp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16.23 7h4.12L10.5 2 2 6.21V17h2V7.4L10.5 4z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M5 8v13h17V8zm15 4-6.5 3.33L7 12v-2l6.5 3.33L20 10z\"\n}, \"1\")], 'MarkAsUnreadSharp');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}