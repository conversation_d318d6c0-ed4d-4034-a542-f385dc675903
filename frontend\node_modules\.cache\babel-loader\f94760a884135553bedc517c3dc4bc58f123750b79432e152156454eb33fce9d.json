{"ast": null, "code": "/**\n * Callback type for the timeout function.\n * Receives current time in milliseconds as an argument.\n */\n\n/**\n * A function that, when called, cancels the timeout.\n */\n\nexport class RequestAnimationFrameTimeoutController {\n  setTimeout(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var startTime = performance.now();\n    var requestId = null;\n    var executeCallback = now => {\n      if (now - startTime >= delay) {\n        callback(now);\n        // tests fail without the extra if, even when five lines below it's not needed\n        // TODO finish transition to the mocked timeout controller and then remove this condition\n      } else if (typeof requestAnimationFrame === 'function') {\n        requestId = requestAnimationFrame(executeCallback);\n      }\n    };\n    requestId = requestAnimationFrame(executeCallback);\n    return () => {\n      cancelAnimationFrame(requestId);\n    };\n  }\n}", "map": {"version": 3, "names": ["RequestAnimationFrameTimeoutController", "setTimeout", "callback", "delay", "arguments", "length", "undefined", "startTime", "performance", "now", "requestId", "executeCallback", "requestAnimationFrame", "cancelAnimationFrame"], "sources": ["D:/Desktop/meror/frontend/node_modules/recharts/es6/animation/timeoutController.js"], "sourcesContent": ["/**\n * Callback type for the timeout function.\n * Receives current time in milliseconds as an argument.\n */\n\n/**\n * A function that, when called, cancels the timeout.\n */\n\nexport class RequestAnimationFrameTimeoutController {\n  setTimeout(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var startTime = performance.now();\n    var requestId = null;\n    var executeCallback = now => {\n      if (now - startTime >= delay) {\n        callback(now);\n        // tests fail without the extra if, even when five lines below it's not needed\n        // TODO finish transition to the mocked timeout controller and then remove this condition\n      } else if (typeof requestAnimationFrame === 'function') {\n        requestId = requestAnimationFrame(executeCallback);\n      }\n    };\n    requestId = requestAnimationFrame(executeCallback);\n    return () => {\n      cancelAnimationFrame(requestId);\n    };\n  }\n}"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,OAAO,MAAMA,sCAAsC,CAAC;EAClDC,UAAUA,CAACC,QAAQ,EAAE;IACnB,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjF,IAAIG,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IACjC,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,eAAe,GAAGF,GAAG,IAAI;MAC3B,IAAIA,GAAG,GAAGF,SAAS,IAAIJ,KAAK,EAAE;QAC5BD,QAAQ,CAACO,GAAG,CAAC;QACb;QACA;MACF,CAAC,MAAM,IAAI,OAAOG,qBAAqB,KAAK,UAAU,EAAE;QACtDF,SAAS,GAAGE,qBAAqB,CAACD,eAAe,CAAC;MACpD;IACF,CAAC;IACDD,SAAS,GAAGE,qBAAqB,CAACD,eAAe,CAAC;IAClD,OAAO,MAAM;MACXE,oBAAoB,CAACH,SAAS,CAAC;IACjC,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}