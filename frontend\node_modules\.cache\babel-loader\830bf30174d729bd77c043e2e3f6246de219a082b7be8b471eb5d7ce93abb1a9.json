{"ast": null, "code": "'use strict';\n\nimport isAbsoluteURL from '../helpers/isAbsoluteURL.js';\nimport combineURLs from '../helpers/combineURLs.js';\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */\nexport default function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {\n  let isRelativeUrl = !isAbsoluteURL(requestedURL);\n  if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n}", "map": {"version": 3, "names": ["isAbsoluteURL", "combineURLs", "buildFullPath", "baseURL", "requestedURL", "allowAbsoluteUrls", "isRelativeUrl"], "sources": ["D:/Desktop/meror/frontend/node_modules/axios/lib/core/buildFullPath.js"], "sourcesContent": ["'use strict';\n\nimport isAbsoluteURL from '../helpers/isAbsoluteURL.js';\nimport combineURLs from '../helpers/combineURLs.js';\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */\nexport default function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {\n  let isRelativeUrl = !isAbsoluteURL(requestedURL);\n  if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,6BAA6B;AACvD,OAAOC,WAAW,MAAM,2BAA2B;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAEC,YAAY,EAAEC,iBAAiB,EAAE;EAC9E,IAAIC,aAAa,GAAG,CAACN,aAAa,CAACI,YAAY,CAAC;EAChD,IAAID,OAAO,KAAKG,aAAa,IAAID,iBAAiB,IAAI,KAAK,CAAC,EAAE;IAC5D,OAAOJ,WAAW,CAACE,OAAO,EAAEC,YAAY,CAAC;EAC3C;EACA,OAAOA,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}