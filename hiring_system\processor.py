import re
from typing import List
from .models import Candidate, ApplicationStatus

class CandidateProcessor:
    def __init__(self):
        self.high_value_skills = ["python", "react", "aws", "kubernetes", "machine learning"]
    
    def score_candidate(self, candidate: Candidate) -> float:
        score = 0
        
        # Experience scoring
        score += min(candidate.years_experience * 10, 50)
        
        # Skills matching
        skill_matches = sum(1 for skill in candidate.skills 
                          if any(hvs in skill.lower() for hvs in self.high_value_skills))
        score += skill_matches * 15
        
        return min(score, 100)
    
    def auto_screen(self, candidates: List[Candidate]) -> List[Candidate]:
        for candidate in candidates:
            candidate.score = self.score_candidate(candidate)
            
            if candidate.score >= 70:
                candidate.status = ApplicationStatus.INTERVIEW
            elif candidate.score >= 40:
                candidate.status = ApplicationStatus.SCREENING
            else:
                candidate.status = ApplicationStatus.REJECTED
                
        return sorted(candidates, key=lambda c: c.score, reverse=True)