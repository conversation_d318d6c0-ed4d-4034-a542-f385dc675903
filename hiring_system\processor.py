import re
import json
from typing import List, Dict, Any, Optional
from collections import Counter
from .models import (
    Candidate, ApplicationStatus, Role, WorkExperience,
    Education, SalaryExpectation, CandidateScore, Team
)

class CandidateProcessor:
    def __init__(self):
        # Define skill categories and their weights
        self.skill_categories = {
            "frontend": ["react", "angular", "vue", "javascript", "typescript", "html/css", "next js", "tailwindcss"],
            "backend": ["python", "java", "c#", "golang", "php", "node js", "express", "flask", "laravel"],
            "database": ["sql", "mongodb", "postgresql", "redis", "mysql"],
            "cloud": ["aws", "azure", "google cloud platform", "docker", "kubernetes"],
            "data_science": ["machine learning", "python", "pandas", "seaborn", "tableau", "data analysis"],
            "mobile": ["react native", "android", "ios", "flutter"],
            "devops": ["docker", "kubernetes", "terraform", "jenkins", "ci/cd"],
            "design": ["ui/ux design", "photoshop", "illustrator", "figma"],
            "management": ["project management", "agile", "scrum", "leadership"]
        }

        # Role requirements mapping
        self.role_requirements = {
            Role.FRONTEND_ENGINEER: {
                "required_skills": ["frontend"],
                "preferred_skills": ["design", "mobile"],
                "min_experience": 2
            },
            Role.BACKEND_ENGINEER: {
                "required_skills": ["backend", "database"],
                "preferred_skills": ["cloud", "devops"],
                "min_experience": 2
            },
            Role.FULLSTACK_ENGINEER: {
                "required_skills": ["frontend", "backend"],
                "preferred_skills": ["database", "cloud"],
                "min_experience": 3
            },
            Role.DATA_SCIENTIST: {
                "required_skills": ["data_science"],
                "preferred_skills": ["cloud", "backend"],
                "min_experience": 2
            },
            Role.PRODUCT_MANAGER: {
                "required_skills": ["management"],
                "preferred_skills": ["frontend", "design"],
                "min_experience": 3
            },
            Role.ENGINEERING_MANAGER: {
                "required_skills": ["management", "backend"],
                "preferred_skills": ["frontend", "cloud"],
                "min_experience": 5
            }
        }

        # Salary budget constraints (annual)
        self.salary_budget = {
            "total_budget": 600000,  # $600k total for 5 people
            "role_ranges": {
                Role.FRONTEND_ENGINEER: (70000, 130000),
                Role.BACKEND_ENGINEER: (80000, 140000),
                Role.FULLSTACK_ENGINEER: (90000, 150000),
                Role.DATA_SCIENTIST: (85000, 145000),
                Role.PRODUCT_MANAGER: (95000, 160000),
                Role.ENGINEERING_MANAGER: (120000, 180000),
                Role.DESIGNER: (65000, 120000),
                Role.LEGAL_COUNSEL: (100000, 180000),
                Role.FINANCIAL_ANALYST: (70000, 130000)
            }
        }

    def load_candidates_from_json(self, file_path: str) -> List[Candidate]:
        """Load candidates from the submission.js file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        candidates = []
        for item in data:
            try:
                # Parse work experiences
                work_experiences = [
                    WorkExperience(
                        company=exp.get("company", ""),
                        role_name=exp.get("roleName", "")
                    ) for exp in item.get("work_experiences", [])
                ]

                # Parse salary expectation
                salary_exp = item.get("annual_salary_expectation", {})
                salary_expectation = SalaryExpectation(
                    full_time=salary_exp.get("full-time"),
                    part_time=salary_exp.get("part-time")
                )

                candidate = Candidate(
                    name=item.get("name", "Unknown"),
                    email=item.get("email", ""),
                    phone=item.get("phone", ""),
                    location=item.get("location", ""),
                    submitted_at=item.get("submitted_at", ""),
                    work_availability=item.get("work_availability", []),
                    annual_salary_expectation=salary_expectation,
                    work_experiences=work_experiences,
                    education=item.get("education", {}),
                    skills=item.get("skills", [])
                )

                candidates.append(candidate)
            except Exception as e:
                print(f"Error processing candidate {item.get('email', 'unknown')}: {e}")
                continue

        return candidates

    def categorize_skills(self, skills: List[str]) -> Dict[str, List[str]]:
        """Categorize candidate skills into predefined categories"""
        categorized = {category: [] for category in self.skill_categories}

        for skill in skills:
            skill_lower = skill.lower()
            for category, category_skills in self.skill_categories.items():
                if any(cat_skill in skill_lower for cat_skill in category_skills):
                    categorized[category].append(skill)

        return categorized

    def score_experience(self, candidate: Candidate) -> float:
        """Score based on work experience quality and quantity"""
        score = 0.0

        # Base experience score (0-30 points)
        years = candidate.years_experience
        score += min(years * 5, 30)

        # Quality of companies and roles (0-20 points)
        senior_roles = ["manager", "director", "lead", "senior", "principal", "architect", "cto", "ceo"]
        tech_companies = ["google", "microsoft", "amazon", "meta", "apple", "netflix", "uber", "airbnb"]

        for exp in candidate.work_experiences:
            role_lower = exp.role_name.lower()
            company_lower = exp.company.lower()

            # Senior role bonus
            if any(senior in role_lower for senior in senior_roles):
                score += 5

            # Tech company bonus
            if any(tech in company_lower for tech in tech_companies):
                score += 3

        return min(score, 50)

    def score_education(self, candidate: Candidate) -> float:
        """Score based on education quality and relevance"""
        score = 0.0

        # Education level scoring
        level_scores = {
            "High School Diploma": 5,
            "Associate's Degree": 10,
            "Bachelor's Degree": 20,
            "Master's Degree": 30,
            "Juris Doctor (J.D)": 25,
            "PhD": 35
        }

        score += level_scores.get(candidate.highest_education_level, 0)

        # Top-tier institution bonus
        if candidate.is_top_tier_education:
            score += 15

        # Relevant degree subjects
        relevant_subjects = [
            "computer science", "information technology", "software engineering",
            "data science", "electrical engineering", "mathematics", "statistics"
        ]

        if isinstance(candidate.education, dict) and "degrees" in candidate.education:
            for degree in candidate.education["degrees"]:
                if isinstance(degree, dict):
                    subject = degree.get("subject", "").lower()
                    if any(rel_sub in subject for rel_sub in relevant_subjects):
                        score += 10
                        break

        return min(score, 50)

    def score_skills(self, candidate: Candidate, target_role: Optional[Role] = None) -> float:
        """Score based on skills relevance and depth"""
        categorized_skills = self.categorize_skills(candidate.skills)
        score = 0.0

        # Base skill diversity score
        categories_with_skills = sum(1 for skills in categorized_skills.values() if skills)
        score += categories_with_skills * 5

        # Role-specific scoring
        if target_role and target_role in self.role_requirements:
            requirements = self.role_requirements[target_role]

            # Required skills
            for req_category in requirements["required_skills"]:
                if categorized_skills[req_category]:
                    score += 20

            # Preferred skills
            for pref_category in requirements.get("preferred_skills", []):
                if categorized_skills[pref_category]:
                    score += 10

        return min(score, 50)

    def score_salary_fit(self, candidate: Candidate, target_role: Optional[Role] = None) -> float:
        """Score based on salary expectation fit"""
        if not target_role or target_role not in self.salary_budget["role_ranges"]:
            return 25  # Neutral score

        salary_str = candidate.annual_salary_expectation.full_time
        if not salary_str:
            return 25

        try:
            # Extract numeric value from salary string
            salary = int(re.sub(r'[^\d]', '', salary_str))
            min_range, max_range = self.salary_budget["role_ranges"][target_role]

            if min_range <= salary <= max_range:
                return 50  # Perfect fit
            elif salary < min_range:
                return 40  # Under budget (good)
            elif salary <= max_range * 1.2:
                return 30  # Slightly over budget
            else:
                return 10  # Way over budget
        except:
            return 25  # Can't parse salary

    def suggest_role(self, candidate: Candidate) -> Optional[Role]:
        """Suggest the best role for a candidate based on their profile"""
        categorized_skills = self.categorize_skills(candidate.skills)
        best_role = None
        best_score = 0

        for role, requirements in self.role_requirements.items():
            role_score = 0

            # Check required skills
            required_met = 0
            for req_category in requirements["required_skills"]:
                if categorized_skills[req_category]:
                    required_met += 1
                    role_score += 30

            # Must meet at least one required skill category
            if required_met == 0:
                continue

            # Check preferred skills
            for pref_category in requirements.get("preferred_skills", []):
                if categorized_skills[pref_category]:
                    role_score += 15

            # Experience requirement
            if candidate.years_experience >= requirements["min_experience"]:
                role_score += 20

            if role_score > best_score:
                best_score = role_score
                best_role = role

        return best_role

    def calculate_comprehensive_score(self, candidate: Candidate) -> CandidateScore:
        """Calculate comprehensive score for a candidate"""
        # Suggest best role first
        suggested_role = self.suggest_role(candidate)
        candidate.suggested_role = suggested_role

        score = CandidateScore()
        score.experience = self.score_experience(candidate)
        score.education = self.score_education(candidate)
        score.skills = self.score_skills(candidate, suggested_role)
        score.salary_fit = self.score_salary_fit(candidate, suggested_role)

        # Role fit score based on suggestion confidence
        if suggested_role:
            score.role_fit = 30
        else:
            score.role_fit = 10

        # Calculate total before diversity bonus
        score.total = (score.experience + score.education + score.skills +
                      score.salary_fit + score.role_fit) / 5

        return score

    def calculate_diversity_bonus(self, candidates: List[Candidate]) -> None:
        """Calculate diversity bonuses for geographic and background diversity"""
        locations = [c.location for c in candidates]
        location_counts = Counter(locations)

        for candidate in candidates:
            diversity_bonus = 0

            # Geographic diversity bonus (less common locations get bonus)
            location_frequency = location_counts[candidate.location] / len(candidates)
            if location_frequency < 0.1:  # Less than 10% representation
                diversity_bonus += 10
            elif location_frequency < 0.2:  # Less than 20% representation
                diversity_bonus += 5

            # Educational background diversity
            if candidate.highest_education_level in ["Juris Doctor (J.D)", "Master's Degree"]:
                diversity_bonus += 5

            # Non-traditional background bonus
            non_tech_subjects = ["law", "business", "finance", "accounting", "economics", "architecture"]
            if isinstance(candidate.education, dict) and "degrees" in candidate.education:
                for degree in candidate.education["degrees"]:
                    if isinstance(degree, dict):
                        subject = degree.get("subject", "").lower()
                        if any(nt_sub in subject for nt_sub in non_tech_subjects):
                            diversity_bonus += 5
                            break

            candidate.score.diversity_bonus = diversity_bonus
            candidate.score.total += diversity_bonus

    def process_candidates(self, candidates: List[Candidate]) -> List[Candidate]:
        """Process all candidates with comprehensive scoring"""
        # Calculate base scores
        for candidate in candidates:
            candidate.score = self.calculate_comprehensive_score(candidate)

        # Add diversity bonuses
        self.calculate_diversity_bonus(candidates)

        # Update status based on scores
        for candidate in candidates:
            total_score = candidate.score.total
            if total_score >= 75:
                candidate.status = ApplicationStatus.INTERVIEW
            elif total_score >= 50:
                candidate.status = ApplicationStatus.SCREENING
            else:
                candidate.status = ApplicationStatus.REJECTED

        return sorted(candidates, key=lambda c: c.score.total, reverse=True)

    def select_optimal_team(self, candidates: List[Candidate], team_size: int = 5) -> Team:
        """Select optimal team considering diversity, skills, and budget"""
        # Filter candidates suitable for hiring
        suitable_candidates = [c for c in candidates if c.score.total >= 50]

        # Define target roles for a balanced team
        target_roles = [
            Role.FULLSTACK_ENGINEER,
            Role.BACKEND_ENGINEER,
            Role.FRONTEND_ENGINEER,
            Role.DATA_SCIENTIST,
            Role.PRODUCT_MANAGER
        ]

        selected_team = []
        used_locations = set()
        total_salary = 0

        # Try to fill each target role with best candidate
        for target_role in target_roles:
            best_candidate = None
            best_score = 0

            for candidate in suitable_candidates:
                if candidate in selected_team:
                    continue

                # Check if candidate can fill this role
                if candidate.suggested_role == target_role or self.can_fill_role(candidate, target_role):
                    # Calculate selection score considering diversity
                    selection_score = candidate.score.total

                    # Geographic diversity bonus
                    if candidate.location not in used_locations:
                        selection_score += 10

                    # Budget fit
                    salary = self.extract_salary(candidate.annual_salary_expectation.full_time)
                    if salary and total_salary + salary <= self.salary_budget["total_budget"]:
                        if selection_score > best_score:
                            best_score = selection_score
                            best_candidate = candidate

            if best_candidate:
                selected_team.append(best_candidate)
                used_locations.add(best_candidate.location)
                salary = self.extract_salary(best_candidate.annual_salary_expectation.full_time)
                if salary:
                    total_salary += salary

        # Fill remaining spots with best available candidates
        while len(selected_team) < team_size and len(selected_team) < len(suitable_candidates):
            best_candidate = None
            best_score = 0

            for candidate in suitable_candidates:
                if candidate in selected_team:
                    continue

                selection_score = candidate.score.total

                # Diversity bonuses
                if candidate.location not in used_locations:
                    selection_score += 15

                # Budget check
                salary = self.extract_salary(candidate.annual_salary_expectation.full_time)
                if salary and total_salary + salary <= self.salary_budget["total_budget"]:
                    if selection_score > best_score:
                        best_score = selection_score
                        best_candidate = candidate

            if best_candidate:
                selected_team.append(best_candidate)
                used_locations.add(best_candidate.location)
                salary = self.extract_salary(best_candidate.annual_salary_expectation.full_time)
                if salary:
                    total_salary += salary
            else:
                break

        return self.create_team_analysis(selected_team, total_salary)

    def can_fill_role(self, candidate: Candidate, role: Role) -> bool:
        """Check if candidate can fill a specific role"""
        if role not in self.role_requirements:
            return False

        requirements = self.role_requirements[role]
        categorized_skills = self.categorize_skills(candidate.skills)

        # Check if candidate meets minimum requirements
        required_skills_met = sum(1 for req_cat in requirements["required_skills"]
                                if categorized_skills[req_cat])

        return (required_skills_met > 0 and
                candidate.years_experience >= requirements["min_experience"])

    def extract_salary(self, salary_str: Optional[str]) -> Optional[int]:
        """Extract numeric salary from string"""
        if not salary_str:
            return None
        try:
            return int(re.sub(r'[^\d]', '', salary_str))
        except:
            return None

    def create_team_analysis(self, team_members: List[Candidate], total_salary: float) -> Team:
        """Create comprehensive team analysis"""
        # Calculate diversity metrics
        locations = [member.location for member in team_members]
        location_distribution = dict(Counter(locations))

        # Calculate skill coverage
        all_skills = []
        for member in team_members:
            all_skills.extend(member.skills)
        skill_coverage = dict(Counter(all_skills))

        # Calculate diversity score
        diversity_score = len(set(locations)) * 20  # Geographic diversity
        diversity_score += len(set(member.suggested_role for member in team_members)) * 15  # Role diversity

        # Generate justification
        justification = self.generate_team_justification(team_members, total_salary, location_distribution)

        return Team(
            id="selected_team_001",
            name="Optimal Startup Team",
            members=team_members,
            total_salary_cost=total_salary,
            diversity_score=diversity_score,
            skill_coverage=skill_coverage,
            geographic_distribution=location_distribution,
            justification=justification
        )

    def generate_team_justification(self, team_members: List[Candidate],
                                  total_salary: float, location_dist: Dict[str, int]) -> str:
        """Generate detailed justification for team selection"""
        justification = f"""
## Team Selection Justification

### Selected Team of {len(team_members)} Members

**Total Annual Salary Cost: ${total_salary:,.0f}** (within ${self.salary_budget['total_budget']:,.0f} budget)

### Team Members:
"""

        for i, member in enumerate(team_members, 1):
            salary = self.extract_salary(member.annual_salary_expectation.full_time)
            justification += f"""
{i}. **{member.name}** ({member.suggested_role.value if member.suggested_role else 'General'})
   - Location: {member.location}
   - Experience: {member.years_experience} positions
   - Education: {member.highest_education_level}
   - Key Skills: {', '.join(member.skills[:5])}
   - Salary: ${salary:,} annually
   - Overall Score: {member.score.total:.1f}/100
   - Why Selected: {self.get_selection_reason(member)}
"""

        justification += f"""
### Diversity Analysis:
- **Geographic Diversity**: {len(location_dist)} different locations ({', '.join(location_dist.keys())})
- **Role Diversity**: Covers {len(set(m.suggested_role for m in team_members))} different specializations
- **Educational Diversity**: Mix of {len(set(m.highest_education_level for m in team_members))} education levels

### Strategic Rationale:
1. **Technical Coverage**: Team covers full-stack development, data science, and product management
2. **Global Perspective**: International team brings diverse market insights
3. **Cost Efficiency**: Total salary within budget while securing top talent
4. **Growth Potential**: Mix of experienced leaders and skilled individual contributors
5. **Cultural Fit**: Diverse backgrounds foster innovation and creative problem-solving
"""

        return justification

    def get_selection_reason(self, candidate: Candidate) -> str:
        """Get specific reason why candidate was selected"""
        reasons = []

        if candidate.score.experience >= 40:
            reasons.append("Strong experience")
        if candidate.score.education >= 35:
            reasons.append("Excellent education")
        if candidate.score.skills >= 40:
            reasons.append("Relevant technical skills")
        if candidate.is_top_tier_education:
            reasons.append("Top-tier education")
        if candidate.score.diversity_bonus > 5:
            reasons.append("Geographic/background diversity")

        return ", ".join(reasons) if reasons else "Well-rounded profile"