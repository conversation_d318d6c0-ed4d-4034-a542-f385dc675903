{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst compareValues = require('../_internal/compareValues.js');\nconst isKey = require('../_internal/isKey.js');\nconst toPath = require('../util/toPath.js');\nfunction orderBy(collection, criteria, orders, guard) {\n  if (collection == null) {\n    return [];\n  }\n  orders = guard ? undefined : orders;\n  if (!Array.isArray(collection)) {\n    collection = Object.values(collection);\n  }\n  if (!Array.isArray(criteria)) {\n    criteria = criteria == null ? [null] : [criteria];\n  }\n  if (criteria.length === 0) {\n    criteria = [null];\n  }\n  if (!Array.isArray(orders)) {\n    orders = orders == null ? [] : [orders];\n  }\n  orders = orders.map(order => String(order));\n  const getValueByNestedPath = (object, path) => {\n    let target = object;\n    for (let i = 0; i < path.length && target != null; ++i) {\n      target = target[path[i]];\n    }\n    return target;\n  };\n  const getValueByCriterion = (criterion, object) => {\n    if (object == null || criterion == null) {\n      return object;\n    }\n    if (typeof criterion === 'object' && 'key' in criterion) {\n      if (Object.hasOwn(object, criterion.key)) {\n        return object[criterion.key];\n      }\n      return getValueByNestedPath(object, criterion.path);\n    }\n    if (typeof criterion === 'function') {\n      return criterion(object);\n    }\n    if (Array.isArray(criterion)) {\n      return getValueByNestedPath(object, criterion);\n    }\n    if (typeof object === 'object') {\n      return object[criterion];\n    }\n    return object;\n  };\n  const preparedCriteria = criteria.map(criterion => {\n    if (Array.isArray(criterion) && criterion.length === 1) {\n      criterion = criterion[0];\n    }\n    if (criterion == null || typeof criterion === 'function' || Array.isArray(criterion) || isKey.isKey(criterion)) {\n      return criterion;\n    }\n    return {\n      key: criterion,\n      path: toPath.toPath(criterion)\n    };\n  });\n  const preparedCollection = collection.map(item => ({\n    original: item,\n    criteria: preparedCriteria.map(criterion => getValueByCriterion(criterion, item))\n  }));\n  return preparedCollection.slice().sort((a, b) => {\n    for (let i = 0; i < preparedCriteria.length; i++) {\n      const comparedResult = compareValues.compareValues(a.criteria[i], b.criteria[i], orders[i]);\n      if (comparedResult !== 0) {\n        return comparedResult;\n      }\n    }\n    return 0;\n  }).map(item => item.original);\n}\nexports.orderBy = orderBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "compareValues", "require", "is<PERSON>ey", "to<PERSON><PERSON>", "orderBy", "collection", "criteria", "orders", "guard", "undefined", "Array", "isArray", "values", "length", "map", "order", "String", "getValueByNestedPath", "object", "path", "target", "i", "getValueByCriterion", "criterion", "hasOwn", "key", "preparedCriteria", "preparedCollection", "item", "original", "slice", "sort", "a", "b", "comparedResult"], "sources": ["D:/Desktop/meror/frontend/node_modules/es-toolkit/dist/compat/array/orderBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst compareValues = require('../_internal/compareValues.js');\nconst isKey = require('../_internal/isKey.js');\nconst toPath = require('../util/toPath.js');\n\nfunction orderBy(collection, criteria, orders, guard) {\n    if (collection == null) {\n        return [];\n    }\n    orders = guard ? undefined : orders;\n    if (!Array.isArray(collection)) {\n        collection = Object.values(collection);\n    }\n    if (!Array.isArray(criteria)) {\n        criteria = criteria == null ? [null] : [criteria];\n    }\n    if (criteria.length === 0) {\n        criteria = [null];\n    }\n    if (!Array.isArray(orders)) {\n        orders = orders == null ? [] : [orders];\n    }\n    orders = orders.map(order => String(order));\n    const getValueByNestedPath = (object, path) => {\n        let target = object;\n        for (let i = 0; i < path.length && target != null; ++i) {\n            target = target[path[i]];\n        }\n        return target;\n    };\n    const getValueByCriterion = (criterion, object) => {\n        if (object == null || criterion == null) {\n            return object;\n        }\n        if (typeof criterion === 'object' && 'key' in criterion) {\n            if (Object.hasOwn(object, criterion.key)) {\n                return object[criterion.key];\n            }\n            return getValueByNestedPath(object, criterion.path);\n        }\n        if (typeof criterion === 'function') {\n            return criterion(object);\n        }\n        if (Array.isArray(criterion)) {\n            return getValueByNestedPath(object, criterion);\n        }\n        if (typeof object === 'object') {\n            return object[criterion];\n        }\n        return object;\n    };\n    const preparedCriteria = criteria.map((criterion) => {\n        if (Array.isArray(criterion) && criterion.length === 1) {\n            criterion = criterion[0];\n        }\n        if (criterion == null || typeof criterion === 'function' || Array.isArray(criterion) || isKey.isKey(criterion)) {\n            return criterion;\n        }\n        return { key: criterion, path: toPath.toPath(criterion) };\n    });\n    const preparedCollection = collection.map(item => ({\n        original: item,\n        criteria: preparedCriteria.map((criterion) => getValueByCriterion(criterion, item)),\n    }));\n    return preparedCollection\n        .slice()\n        .sort((a, b) => {\n        for (let i = 0; i < preparedCriteria.length; i++) {\n            const comparedResult = compareValues.compareValues(a.criteria[i], b.criteria[i], orders[i]);\n            if (comparedResult !== 0) {\n                return comparedResult;\n            }\n        }\n        return 0;\n    })\n        .map(item => item.original);\n}\n\nexports.orderBy = orderBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,aAAa,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AAC9D,MAAMC,KAAK,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC9C,MAAME,MAAM,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAE3C,SAASG,OAAOA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAClD,IAAIH,UAAU,IAAI,IAAI,EAAE;IACpB,OAAO,EAAE;EACb;EACAE,MAAM,GAAGC,KAAK,GAAGC,SAAS,GAAGF,MAAM;EACnC,IAAI,CAACG,KAAK,CAACC,OAAO,CAACN,UAAU,CAAC,EAAE;IAC5BA,UAAU,GAAGX,MAAM,CAACkB,MAAM,CAACP,UAAU,CAAC;EAC1C;EACA,IAAI,CAACK,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;IAC1BA,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAACA,QAAQ,CAAC;EACrD;EACA,IAAIA,QAAQ,CAACO,MAAM,KAAK,CAAC,EAAE;IACvBP,QAAQ,GAAG,CAAC,IAAI,CAAC;EACrB;EACA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IACxBA,MAAM,GAAGA,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,MAAM,CAAC;EAC3C;EACAA,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACC,KAAK,IAAIC,MAAM,CAACD,KAAK,CAAC,CAAC;EAC3C,MAAME,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK;IAC3C,IAAIC,MAAM,GAAGF,MAAM;IACnB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACN,MAAM,IAAIO,MAAM,IAAI,IAAI,EAAE,EAAEC,CAAC,EAAE;MACpDD,MAAM,GAAGA,MAAM,CAACD,IAAI,CAACE,CAAC,CAAC,CAAC;IAC5B;IACA,OAAOD,MAAM;EACjB,CAAC;EACD,MAAME,mBAAmB,GAAGA,CAACC,SAAS,EAAEL,MAAM,KAAK;IAC/C,IAAIA,MAAM,IAAI,IAAI,IAAIK,SAAS,IAAI,IAAI,EAAE;MACrC,OAAOL,MAAM;IACjB;IACA,IAAI,OAAOK,SAAS,KAAK,QAAQ,IAAI,KAAK,IAAIA,SAAS,EAAE;MACrD,IAAI7B,MAAM,CAAC8B,MAAM,CAACN,MAAM,EAAEK,SAAS,CAACE,GAAG,CAAC,EAAE;QACtC,OAAOP,MAAM,CAACK,SAAS,CAACE,GAAG,CAAC;MAChC;MACA,OAAOR,oBAAoB,CAACC,MAAM,EAAEK,SAAS,CAACJ,IAAI,CAAC;IACvD;IACA,IAAI,OAAOI,SAAS,KAAK,UAAU,EAAE;MACjC,OAAOA,SAAS,CAACL,MAAM,CAAC;IAC5B;IACA,IAAIR,KAAK,CAACC,OAAO,CAACY,SAAS,CAAC,EAAE;MAC1B,OAAON,oBAAoB,CAACC,MAAM,EAAEK,SAAS,CAAC;IAClD;IACA,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAE;MAC5B,OAAOA,MAAM,CAACK,SAAS,CAAC;IAC5B;IACA,OAAOL,MAAM;EACjB,CAAC;EACD,MAAMQ,gBAAgB,GAAGpB,QAAQ,CAACQ,GAAG,CAAES,SAAS,IAAK;IACjD,IAAIb,KAAK,CAACC,OAAO,CAACY,SAAS,CAAC,IAAIA,SAAS,CAACV,MAAM,KAAK,CAAC,EAAE;MACpDU,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC;IAC5B;IACA,IAAIA,SAAS,IAAI,IAAI,IAAI,OAAOA,SAAS,KAAK,UAAU,IAAIb,KAAK,CAACC,OAAO,CAACY,SAAS,CAAC,IAAIrB,KAAK,CAACA,KAAK,CAACqB,SAAS,CAAC,EAAE;MAC5G,OAAOA,SAAS;IACpB;IACA,OAAO;MAAEE,GAAG,EAAEF,SAAS;MAAEJ,IAAI,EAAEhB,MAAM,CAACA,MAAM,CAACoB,SAAS;IAAE,CAAC;EAC7D,CAAC,CAAC;EACF,MAAMI,kBAAkB,GAAGtB,UAAU,CAACS,GAAG,CAACc,IAAI,KAAK;IAC/CC,QAAQ,EAAED,IAAI;IACdtB,QAAQ,EAAEoB,gBAAgB,CAACZ,GAAG,CAAES,SAAS,IAAKD,mBAAmB,CAACC,SAAS,EAAEK,IAAI,CAAC;EACtF,CAAC,CAAC,CAAC;EACH,OAAOD,kBAAkB,CACpBG,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAChB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,gBAAgB,CAACb,MAAM,EAAEQ,CAAC,EAAE,EAAE;MAC9C,MAAMa,cAAc,GAAGlC,aAAa,CAACA,aAAa,CAACgC,CAAC,CAAC1B,QAAQ,CAACe,CAAC,CAAC,EAAEY,CAAC,CAAC3B,QAAQ,CAACe,CAAC,CAAC,EAAEd,MAAM,CAACc,CAAC,CAAC,CAAC;MAC3F,IAAIa,cAAc,KAAK,CAAC,EAAE;QACtB,OAAOA,cAAc;MACzB;IACJ;IACA,OAAO,CAAC;EACZ,CAAC,CAAC,CACGpB,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;AACnC;AAEAjC,OAAO,CAACQ,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}