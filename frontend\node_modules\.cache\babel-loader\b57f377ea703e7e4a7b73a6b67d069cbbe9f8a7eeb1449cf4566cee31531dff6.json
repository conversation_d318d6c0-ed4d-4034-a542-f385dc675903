{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isEqualWith = require('./isEqualWith.js');\nconst noop = require('../function/noop.js');\nfunction isEqual(a, b) {\n  return isEqualWith.isEqualWith(a, b, noop.noop);\n}\nexports.isEqual = isEqual;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isEqualWith", "require", "noop", "isEqual", "a", "b"], "sources": ["D:/Desktop/meror/frontend/node_modules/es-toolkit/dist/predicate/isEqual.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isEqualWith = require('./isEqualWith.js');\nconst noop = require('../function/noop.js');\n\nfunction isEqual(a, b) {\n    return isEqualWith.isEqualWith(a, b, noop.noop);\n}\n\nexports.isEqual = isEqual;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,WAAW,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC/C,MAAMC,IAAI,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAE3C,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnB,OAAOL,WAAW,CAACA,WAAW,CAACI,CAAC,EAAEC,CAAC,EAAEH,IAAI,CAACA,IAAI,CAAC;AACnD;AAEAN,OAAO,CAACO,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}