[{"D:\\Desktop\\meror\\frontend\\src\\index.js": "1", "D:\\Desktop\\meror\\frontend\\src\\App.js": "2", "D:\\Desktop\\meror\\frontend\\src\\reportWebVitals.js": "3", "D:\\Desktop\\meror\\frontend\\src\\components\\CandidateDetail.js": "4", "D:\\Desktop\\meror\\frontend\\src\\components\\TeamSelection.js": "5", "D:\\Desktop\\meror\\frontend\\src\\components\\Dashboard.js": "6", "D:\\Desktop\\meror\\frontend\\src\\components\\Analytics.js": "7", "D:\\Desktop\\meror\\frontend\\src\\components\\Navigation.js": "8"}, {"size": 535, "mtime": 1752851857550, "results": "9", "hashOfConfig": "10"}, {"size": 1663, "mtime": 1752852018116, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1752851857554, "results": "12", "hashOfConfig": "10"}, {"size": 10182, "mtime": 1752852117621, "results": "13", "hashOfConfig": "10"}, {"size": 12928, "mtime": 1752852171239, "results": "14", "hashOfConfig": "10"}, {"size": 10554, "mtime": 1752852079522, "results": "15", "hashOfConfig": "10"}, {"size": 11833, "mtime": 1752852214510, "results": "16", "hashOfConfig": "10"}, {"size": 1767, "mtime": 1752852042078, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1btq6mz", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Desktop\\meror\\frontend\\src\\index.js", [], [], "D:\\Desktop\\meror\\frontend\\src\\App.js", [], [], "D:\\Desktop\\meror\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Desktop\\meror\\frontend\\src\\components\\CandidateDetail.js", ["42", "43", "44"], [], "D:\\Desktop\\meror\\frontend\\src\\components\\TeamSelection.js", ["45", "46"], [], "D:\\Desktop\\meror\\frontend\\src\\components\\Dashboard.js", ["47", "48"], [], "D:\\Desktop\\meror\\frontend\\src\\components\\Analytics.js", [], [], "D:\\Desktop\\meror\\frontend\\src\\components\\Navigation.js", ["49"], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 17, "column": 3, "nodeType": "52", "messageId": "53", "endLine": 17, "endColumn": 8}, {"ruleId": "50", "severity": 1, "message": "54", "line": 26, "column": 3, "nodeType": "52", "messageId": "53", "endLine": 26, "endColumn": 7}, {"ruleId": "55", "severity": 1, "message": "56", "line": 45, "column": 6, "nodeType": "57", "endLine": 45, "endColumn": 10, "suggestions": "58"}, {"ruleId": "50", "severity": 1, "message": "59", "line": 18, "column": 3, "nodeType": "52", "messageId": "53", "endLine": 18, "endColumn": 8}, {"ruleId": "50", "severity": 1, "message": "60", "line": 28, "column": 3, "nodeType": "52", "messageId": "53", "endLine": 28, "endColumn": 7}, {"ruleId": "50", "severity": 1, "message": "61", "line": 18, "column": 3, "nodeType": "52", "messageId": "53", "endLine": 18, "endColumn": 9}, {"ruleId": "55", "severity": 1, "message": "62", "line": 50, "column": 6, "nodeType": "57", "endLine": 50, "endColumn": 27, "suggestions": "63"}, {"ruleId": "50", "severity": 1, "message": "64", "line": 11, "column": 13, "nodeType": "52", "messageId": "53", "endLine": 11, "endColumn": 23}, "no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "'Work' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCandidate'. Either include it or remove the dependency array.", "ArrayExpression", ["65"], "'Alert' is defined but never used.", "'Star' is defined but never used.", "'Rating' is defined but never used.", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["66"], "'PeopleIcon' is defined but never used.", {"desc": "67", "fix": "68"}, {"desc": "69", "fix": "70"}, "Update the dependencies array to be: [fetchCandidate, id]", {"range": "71", "text": "72"}, "Update the dependencies array to be: [applyFilters, candidates, filters]", {"range": "73", "text": "74"}, [796, 800], "[fetchCandidate, id]", [920, 941], "[applyFilters, candidates, filters]"]